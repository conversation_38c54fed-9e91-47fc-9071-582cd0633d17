// 贝塞尔曲线核心代码库
const BezierCoreCodes = {
    linear: {
        title: "线性贝塞尔曲线核心代码",
        description: "最简单的贝塞尔曲线，由两个控制点定义的直线插值",
        code: `// 线性贝塞尔曲线计算函数
function calculateLinearBezier(p0, p1, t) {
    // 参数方程: B(t) = (1-t)P₀ + tP₁
    // 其中 t ∈ [0,1]，P₀和P₁是两个控制点
    
    return {
        x: (1 - t) * p0.x + t * p1.x,
        y: (1 - t) * p0.y + t * p1.y
    };
}

// 绘制线性贝塞尔曲线
function drawLinearBezier(ctx, p0, p1) {
    ctx.beginPath();
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    
    // 使用多个点绘制平滑曲线
    const steps = 100;
    for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const point = calculateLinearBezier(p0, p1, t);
        
        if (i === 0) {
            ctx.moveTo(point.x, point.y);
        } else {
            ctx.lineTo(point.x, point.y);
        }
    }
    
    ctx.stroke();
}

// 使用示例
const startPoint = { x: 100, y: 200 };
const endPoint = { x: 500, y: 200 };

// 获取t=0.5时的点（中点）
const midPoint = calculateLinearBezier(startPoint, endPoint, 0.5);
console.log('中点坐标:', midPoint); // { x: 300, y: 200 }

// 在Canvas上绘制
const canvas = document.getElementById('myCanvas');
const ctx = canvas.getContext('2d');
drawLinearBezier(ctx, startPoint, endPoint);`
    },
    
    quadratic: {
        title: "二次贝塞尔曲线核心代码",
        description: "由三个控制点定义的抛物线，广泛应用于字体设计和图形绘制",
        code: `// 二次贝塞尔曲线计算函数
function calculateQuadraticBezier(p0, p1, p2, t) {
    // 参数方程: B(t) = (1-t)²P₀ + 2(1-t)tP₁ + t²P₂
    // 其中 t ∈ [0,1]，P₀、P₁、P₂是三个控制点
    
    const u = 1 - t;
    const u2 = u * u;
    const t2 = t * t;
    
    return {
        x: u2 * p0.x + 2 * u * t * p1.x + t2 * p2.x,
        y: u2 * p0.y + 2 * u * t * p1.y + t2 * p2.y
    };
}

// 德卡斯特里奥算法实现（二次）
function quadraticDeCasteljau(p0, p1, p2, t) {
    // 第一层插值
    const q0 = {
        x: (1 - t) * p0.x + t * p1.x,
        y: (1 - t) * p0.y + t * p1.y
    };
    
    const q1 = {
        x: (1 - t) * p1.x + t * p2.x,
        y: (1 - t) * p1.y + t * p2.y
    };
    
    // 第二层插值（最终结果）
    return {
        x: (1 - t) * q0.x + t * q1.x,
        y: (1 - t) * q0.y + t * q1.y
    };
}

// 绘制二次贝塞尔曲线
function drawQuadraticBezier(ctx, p0, p1, p2) {
    ctx.beginPath();
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    
    const steps = 100;
    for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const point = calculateQuadraticBezier(p0, p1, p2, t);
        
        if (i === 0) {
            ctx.moveTo(point.x, point.y);
        } else {
            ctx.lineTo(point.x, point.y);
        }
    }
    
    ctx.stroke();
}

// 使用示例
const startPoint = { x: 100, y: 300 };
const controlPoint = { x: 300, y: 100 };
const endPoint = { x: 500, y: 300 };

// 获取t=0.5时的点
const midPoint = calculateQuadraticBezier(startPoint, controlPoint, endPoint, 0.5);
console.log('曲线中点:', midPoint);

// 在Canvas上绘制
const canvas = document.getElementById('myCanvas');
const ctx = canvas.getContext('2d');
drawQuadraticBezier(ctx, startPoint, controlPoint, endPoint);`
    },
    
    cubic: {
        title: "三次贝塞尔曲线核心代码",
        description: "最重要的贝塞尔曲线，由四个控制点定义，广泛应用于计算机图形学",
        code: `// 三次贝塞尔曲线计算函数
function calculateCubicBezier(p0, p1, p2, p3, t) {
    // 参数方程: B(t) = (1-t)³P₀ + 3(1-t)²tP₁ + 3(1-t)t²P₂ + t³P₃
    // 其中 t ∈ [0,1]，P₀、P₁、P₂、P₃是四个控制点
    
    const u = 1 - t;
    const u2 = u * u;
    const u3 = u2 * u;
    const t2 = t * t;
    const t3 = t2 * t;
    
    return {
        x: u3 * p0.x + 3 * u2 * t * p1.x + 3 * u * t2 * p2.x + t3 * p3.x,
        y: u3 * p0.y + 3 * u2 * t * p1.y + 3 * u * t2 * p2.y + t3 * p3.y
    };
}

// 德卡斯特里奥算法实现（三次）
function cubicDeCasteljau(p0, p1, p2, p3, t) {
    // 第一层插值
    const q0 = lerp(p0, p1, t);
    const q1 = lerp(p1, p2, t);
    const q2 = lerp(p2, p3, t);
    
    // 第二层插值
    const r0 = lerp(q0, q1, t);
    const r1 = lerp(q1, q2, t);
    
    // 第三层插值（最终结果）
    return lerp(r0, r1, t);
}

// 线性插值辅助函数
function lerp(p1, p2, t) {
    return {
        x: (1 - t) * p1.x + t * p2.x,
        y: (1 - t) * p1.y + t * p2.y
    };
}

// 计算三次贝塞尔曲线的切线
function getCubicBezierTangent(p0, p1, p2, p3, t) {
    // 一阶导数: B'(t) = 3(1-t)²(P₁-P₀) + 6(1-t)t(P₂-P₁) + 3t²(P₃-P₂)
    const u = 1 - t;
    const u2 = u * u;
    const t2 = t * t;
    
    return {
        x: 3 * u2 * (p1.x - p0.x) + 6 * u * t * (p2.x - p1.x) + 3 * t2 * (p3.x - p2.x),
        y: 3 * u2 * (p1.y - p0.y) + 6 * u * t * (p2.y - p1.y) + 3 * t2 * (p3.y - p2.y)
    };
}

// 绘制三次贝塞尔曲线
function drawCubicBezier(ctx, p0, p1, p2, p3) {
    ctx.beginPath();
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    
    const steps = 100;
    for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const point = calculateCubicBezier(p0, p1, p2, p3, t);
        
        if (i === 0) {
            ctx.moveTo(point.x, point.y);
        } else {
            ctx.lineTo(point.x, point.y);
        }
    }
    
    ctx.stroke();
}

// 使用示例
const p0 = { x: 100, y: 300 };
const p1 = { x: 200, y: 100 };
const p2 = { x: 400, y: 100 };
const p3 = { x: 500, y: 300 };

// 获取t=0.5时的点和切线
const midPoint = calculateCubicBezier(p0, p1, p2, p3, 0.5);
const tangent = getCubicBezierTangent(p0, p1, p2, p3, 0.5);
console.log('曲线中点:', midPoint);
console.log('中点切线:', tangent);

// 在Canvas上绘制
const canvas = document.getElementById('myCanvas');
const ctx = canvas.getContext('2d');
drawCubicBezier(ctx, p0, p1, p2, p3);`
    },
    
    quartic: {
        title: "四次贝塞尔曲线核心代码",
        description: "由五个控制点定义的高次贝塞尔曲线，提供更高的灵活性和控制精度",
        code: `// 四次贝塞尔曲线计算函数
function calculateQuarticBezier(p0, p1, p2, p3, p4, t) {
    // 参数方程: B(t) = (1-t)⁴P₀ + 4(1-t)³tP₁ + 6(1-t)²t²P₂ + 4(1-t)t³P₃ + t⁴P₄
    // 其中 t ∈ [0,1]，P₀到P₄是五个控制点
    
    const u = 1 - t;
    const u2 = u * u;
    const u3 = u2 * u;
    const u4 = u3 * u;
    const t2 = t * t;
    const t3 = t2 * t;
    const t4 = t3 * t;
    
    return {
        x: u4 * p0.x + 4 * u3 * t * p1.x + 6 * u2 * t2 * p2.x + 4 * u * t3 * p3.x + t4 * p4.x,
        y: u4 * p0.y + 4 * u3 * t * p1.y + 6 * u2 * t2 * p2.y + 4 * u * t3 * p3.y + t4 * p4.y
    };
}

// 通用德卡斯特里奥算法（适用于任意次数）
function generalDeCasteljau(points, t) {
    if (points.length === 1) {
        return points[0];
    }
    
    const newPoints = [];
    for (let i = 0; i < points.length - 1; i++) {
        newPoints.push({
            x: (1 - t) * points[i].x + t * points[i + 1].x,
            y: (1 - t) * points[i].y + t * points[i + 1].y
        });
    }
    
    return generalDeCasteljau(newPoints, t);
}

// 伯恩斯坦基函数（四次）
function bernsteinBasis4(i, t) {
    const u = 1 - t;
    switch (i) {
        case 0: return u * u * u * u;                    // (1-t)⁴
        case 1: return 4 * u * u * u * t;                // 4(1-t)³t
        case 2: return 6 * u * u * t * t;                // 6(1-t)²t²
        case 3: return 4 * u * t * t * t;                // 4(1-t)t³
        case 4: return t * t * t * t;                    // t⁴
        default: return 0;
    }
}

// 使用伯恩斯坦基函数计算四次贝塞尔曲线
function calculateQuarticBezierBernstein(points, t) {
    let x = 0, y = 0;
    
    for (let i = 0; i < points.length; i++) {
        const basis = bernsteinBasis4(i, t);
        x += basis * points[i].x;
        y += basis * points[i].y;
    }
    
    return { x, y };
}

// 绘制四次贝塞尔曲线
function drawQuarticBezier(ctx, p0, p1, p2, p3, p4) {
    ctx.beginPath();
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    
    const steps = 100;
    for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const point = calculateQuarticBezier(p0, p1, p2, p3, p4, t);
        
        if (i === 0) {
            ctx.moveTo(point.x, point.y);
        } else {
            ctx.lineTo(point.x, point.y);
        }
    }
    
    ctx.stroke();
}

// 使用示例
const points = [
    { x: 80, y: 320 },   // P₀
    { x: 180, y: 80 },   // P₁
    { x: 300, y: 320 },  // P₂
    { x: 420, y: 80 },   // P₃
    { x: 520, y: 320 }   // P₄
];

// 获取t=0.5时的点
const midPoint = calculateQuarticBezier(...points, 0.5);
console.log('曲线中点:', midPoint);

// 使用德卡斯特里奥算法验证
const midPointDeCasteljau = generalDeCasteljau(points, 0.5);
console.log('德卡斯特里奥算法结果:', midPointDeCasteljau);

// 在Canvas上绘制
const canvas = document.getElementById('myCanvas');
const ctx = canvas.getContext('2d');
drawQuarticBezier(ctx, ...points);`
    }
};

// 显示核心代码的函数
function showCoreCode(curveType) {
    const codeData = BezierCoreCodes[curveType];
    if (!codeData) {
        alert('未找到该曲线类型的核心代码');
        return;
    }
    
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'code-modal';
    modal.innerHTML = `
        <div class="code-modal-content">
            <div class="code-modal-header">
                <h3>${codeData.title}</h3>
                <button class="code-modal-close" onclick="closeCoreCodeModal()">&times;</button>
            </div>
            <div class="code-modal-body">
                <p class="code-description">${codeData.description}</p>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-language">JavaScript</span>
                        <button class="copy-code-btn" onclick="copyCodeToClipboard('${curveType}')">
                            📋 复制代码
                        </button>
                    </div>
                    <pre class="code-content" id="code-${curveType}"><code>${codeData.code}</code></pre>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 防止背景滚动
    document.body.style.overflow = 'hidden';
    
    // 点击外部关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeCoreCodeModal();
        }
    });
    
    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeCoreCodeModal();
        }
    });
}

// 关闭核心代码模态框
function closeCoreCodeModal() {
    const modal = document.querySelector('.code-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// 复制代码到剪贴板
function copyCodeToClipboard(curveType) {
    const codeElement = document.getElementById(`code-${curveType}`);
    const codeText = BezierCoreCodes[curveType].code;
    
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = codeText;
    document.body.appendChild(textArea);
    textArea.select();
    
    try {
        document.execCommand('copy');
        
        // 更新按钮文本
        const copyBtn = document.querySelector('.copy-code-btn');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '✅ 已复制!';
        copyBtn.style.background = 'linear-gradient(135deg, #48bb78, #38a169)';
        
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.style.background = '';
        }, 2000);
        
    } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择代码');
    }
    
    document.body.removeChild(textArea);
}

// 导出到全局作用域
window.BezierCoreCodes = BezierCoreCodes;
window.showCoreCode = showCoreCode;
window.closeCoreCodeModal = closeCoreCodeModal;
window.copyCodeToClipboard = copyCodeToClipboard;
