<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件扩展机制 - 交互式学习演示</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="container">
        <!-- 标题和说明 -->
        <header class="header">
            <h1>🔌 插件扩展机制演示</h1>
            <p class="subtitle">体验软件架构中的模块化设计思想 - 像搭积木一样扩展系统功能</p>
        </header>

        <!-- 核心概念说明 -->
        <section class="concept-section">
            <div class="concept-card">
                <h3>💡 什么是插件扩展机制？</h3>
                <p>插件扩展机制是一种软件架构设计思想，它允许将功能模块化，使这些模块（插件）可以<strong>独立开发、动态加载、灵活替换</strong>，而无需修改主系统核心代码。</p>
                <div class="analogy">
                    <span class="analogy-icon">🔌</span>
                    <p><strong>生活类比：</strong>就像万能插座，主程序是插排，插件是家电。只要接口匹配，家电就可以随时插入使用，互不干扰。</p>
                </div>
            </div>
        </section>

        <!-- 主要演示区域 -->
        <div class="demo-area">
            <!-- 插件库 -->
            <div class="plugin-library">
                <h3>🧩 可用插件库</h3>
                <p class="library-desc">拖拽插件到右侧AI系统中进行安装</p>
                <div class="plugins-container">
                    <div class="plugin" draggable="true" data-plugin="translator" data-interface="ITranslator">
                        <div class="plugin-icon">🌐</div>
                        <div class="plugin-name">翻译插件</div>
                        <div class="plugin-desc">多语言翻译功能</div>
                        <div class="plugin-version">v1.2.0</div>
                    </div>

                    <div class="plugin" draggable="true" data-plugin="calculator" data-interface="ICalculator">
                        <div class="plugin-icon">🧮</div>
                        <div class="plugin-name">计算器插件</div>
                        <div class="plugin-desc">数学计算功能</div>
                        <div class="plugin-version">v2.1.0</div>
                    </div>

                    <div class="plugin" draggable="true" data-plugin="weather" data-interface="IWeather">
                        <div class="plugin-icon">🌤️</div>
                        <div class="plugin-name">天气插件</div>
                        <div class="plugin-desc">天气查询功能</div>
                        <div class="plugin-version">v1.0.3</div>
                    </div>

                    <div class="plugin" draggable="true" data-plugin="timer" data-interface="ITimer">
                        <div class="plugin-icon">⏰</div>
                        <div class="plugin-name">定时器插件</div>
                        <div class="plugin-desc">时间管理功能</div>
                        <div class="plugin-version">v1.5.2</div>
                    </div>

                    <div class="plugin" draggable="true" data-plugin="notepad" data-interface="ITextEditor">
                        <div class="plugin-icon">📝</div>
                        <div class="plugin-name">记事本插件</div>
                        <div class="plugin-desc">文本编辑功能</div>
                        <div class="plugin-version">v3.0.1</div>
                    </div>

                    <div class="plugin" draggable="true" data-plugin="imageProcessor" data-interface="IImageProcessor">
                        <div class="plugin-icon">🖼️</div>
                        <div class="plugin-name">图像处理插件</div>
                        <div class="plugin-desc">图片编辑与滤镜</div>
                        <div class="plugin-version">v2.3.0</div>
                    </div>
                </div>
            </div>

            <!-- 主系统 -->
            <div class="main-system">
                <h3>🤖 智能AI助手核心系统</h3>
                <div class="system-architecture">
                    <div class="core-layer">
                        <h4>🏗️ 核心架构层</h4>
                        <div class="architecture-item">插件管理器</div>
                        <div class="architecture-item">接口注册中心</div>
                        <div class="architecture-item">事件总线</div>
                    </div>
                </div>
                <div class="system-core" id="systemCore">
                    <div class="core-status">
                        <div class="status-indicator active"></div>
                        <span id="systemStatus">基础系统运行中 - 等待插件扩展...</span>
                    </div>
                    <div class="drop-zone" id="dropZone">
                        <div class="drop-zone-content">
                            <div class="drop-icon">🔌</div>
                            <p>将插件拖拽到这里进行安装</p>
                            <div class="drop-hint">支持热插拔 • 动态加载 • 即插即用</div>
                        </div>
                    </div>
                    <div class="installed-plugins" id="installedPlugins">
                        <h4>📦 已安装插件：</h4>
                        <div class="plugin-list" id="pluginList">
                            <div class="empty-state">暂无插件，拖拽插件到上方区域进行安装</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能测试区 -->
        <div class="test-area" id="testArea">
            <h3>🧪 功能测试区</h3>
            <div class="test-tabs" id="testTabs">
                <div class="tab-buttons" id="tabButtons">
                    <button class="tab-btn active" data-tab="overview">系统概览</button>
                </div>
                <div class="tab-content" id="tabContent">
                    <div class="tab-pane active" id="overview">
                        <div class="system-overview">
                            <div class="capability-grid">
                                <div class="capability-item">
                                    <span class="capability-icon">🔧</span>
                                    <span>基础对话能力</span>
                                    <span class="status enabled">✓</span>
                                </div>
                                <div class="capability-item">
                                    <span class="capability-icon">🧠</span>
                                    <span>逻辑推理能力</span>
                                    <span class="status enabled">✓</span>
                                </div>
                            </div>
                            <p class="overview-hint">安装插件后，系统将获得更多能力，并在此处显示对应的测试界面</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统日志 -->
        <div class="system-log">
            <h3>📋 系统运行日志</h3>
            <div class="log-controls">
                <button class="log-btn" onclick="clearLog()">清空日志</button>
                <span class="log-count">日志条数: <span id="logCount">1</span></span>
            </div>
            <div class="log-content" id="logContent">
                <div class="log-entry system">
                    <span class="log-time">[00:00:01]</span>
                    <span class="log-type">[SYSTEM]</span>
                    <span class="log-message">AI助手核心系统启动完成，插件管理器就绪...</span>
                </div>
            </div>
        </div>

        <!-- 知识点总结 -->
        <section class="knowledge-section">
            <h3>📚 插件扩展机制核心知识点</h3>
            <div class="knowledge-grid">
                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔧</span>
                        <h4>接口标准化</h4>
                    </div>
                    <p><strong>原理：</strong>插件必须遵循统一的接口规范（如IPlugin接口），确保与主系统兼容</p>
                    <p><strong>好处：</strong>任何符合接口的插件都能无缝接入，降低耦合度</p>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔄</span>
                        <h4>动态加载</h4>
                    </div>
                    <p><strong>原理：</strong>插件可以在运行时动态安装和卸载，无需重启系统</p>
                    <p><strong>好处：</strong>提高系统灵活性，支持热更新和热插拔</p>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🛡️</span>
                        <h4>模块隔离性</h4>
                    </div>
                    <p><strong>原理：</strong>插件之间相互独立，通过沙箱机制隔离运行</p>
                    <p><strong>好处：</strong>一个插件的问题不会影响其他功能，提高系统稳定性</p>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🎯</span>
                        <h4>无限可扩展性</h4>
                    </div>
                    <p><strong>原理：</strong>通过插件机制，系统功能可以无限扩展而不修改核心代码</p>
                    <p><strong>好处：</strong>遵循开闭原则，对扩展开放，对修改封闭</p>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔌</span>
                        <h4>依赖注入</h4>
                    </div>
                    <p><strong>原理：</strong>主系统通过依赖注入容器管理插件生命周期</p>
                    <p><strong>好处：</strong>实现控制反转，降低系统复杂度</p>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">📡</span>
                        <h4>事件驱动通信</h4>
                    </div>
                    <p><strong>原理：</strong>插件间通过事件总线进行松耦合通信</p>
                    <p><strong>好处：</strong>插件无需直接依赖，提高系统的可维护性</p>
                </div>
            </div>
        </section>

        <!-- 核心代码按钮 -->
        <div class="code-section">
            <button class="code-btn" onclick="showCoreCode()">
                🔍 查看实现该效果的核心代码
            </button>
        </div>
    </div>

    <!-- 代码展示模态框 -->
    <div class="modal" id="codeModal">
        <div class="modal-content">
            <span class="close" onclick="closeCoreCode()">&times;</span>
            <h3>核心实现代码</h3>
            <pre id="codeDisplay"></pre>
        </div>
    </div>

    <script src="main.js"></script>
    <script src="code.js"></script>
</body>

</html>