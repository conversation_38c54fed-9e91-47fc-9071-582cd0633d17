/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.2em;
    color: #666;
    margin: 0;
}

/* 概念说明区域 */
.concept-section {
    margin-bottom: 30px;
}

.concept-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #667eea;
}

.concept-card h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.concept-card p {
    line-height: 1.6;
    margin-bottom: 15px;
}

.analogy {
    background: #f8f9ff;
    padding: 15px;
    border-radius: 10px;
    border-left: 3px solid #667eea;
    margin-top: 15px;
}

.analogy-icon {
    font-size: 1.5em;
    margin-right: 10px;
}

/* 架构展示区域 */
.architecture-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.architecture-section h3 {
    color: #667eea;
    margin-bottom: 25px;
    font-size: 1.4em;
    text-align: center;
}

.architecture-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr auto 1fr;
    gap: 20px;
    align-items: start;
    margin-bottom: 30px;
}

/* 层级样式 */
.layer {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 20px;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    position: relative;
}

.layer.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.02);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
}

.layer-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.layer-icon {
    font-size: 2em;
    display: block;
    margin-bottom: 10px;
}

.layer-header h4 {
    color: #667eea;
    font-size: 1.1em;
    margin: 0;
}

.layer.active .layer-header h4 {
    color: white;
}

.layer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.layer-status {
    text-align: center;
    padding: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9em;
    margin-top: 15px;
}

.layer.active .layer-status {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Host层特殊样式 */
.host-layer {
    border-left: 5px solid #4CAF50;
}

.user-interface h5 {
    color: #4CAF50;
    margin-bottom: 15px;
    text-align: center;
}

.request-form {
    background: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.request-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.request-form select,
.request-form textarea {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 0.9em;
    font-family: inherit;
}

.request-form select:focus,
.request-form textarea:focus {
    outline: none;
    border-color: #4CAF50;
}

.request-form button {
    width: 100%;
    padding: 12px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
    font-size: 1em;
}

.request-form button:hover:not(:disabled) {
    background: #45a049;
}

.request-form button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.response-area {
    background: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 10px;
    flex: 1;
}

.response-area h6 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1em;
}

.response-content {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    min-height: 80px;
    border-left: 3px solid #4CAF50;
}

/* Client层特殊样式 */
.client-layer {
    border-left: 5px solid #FF9800;
}

.client-functions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.function-item {
    background: rgba(255, 255, 255, 0.9);
    padding: 12px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.function-icon {
    font-size: 1.2em;
}

.function-info {
    flex: 1;
}

.function-info h6 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 0.9em;
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    background: #f0f0f0;
    color: #666;
    transition: all 0.3s ease;
}

.status-indicator.active {
    background: #4CAF50;
    color: white;
}

.status-indicator.processing {
    background: #FF9800;
    color: white;
    animation: pulse 1s infinite;
}

.processing-log {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    flex: 1;
}

.processing-log h6 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 0.9em;
}

.log-content {
    background: #1a1a1a;
    color: #00ff00;
    padding: 10px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.8em;
    max-height: 120px;
    overflow-y: auto;
}

.log-entry {
    margin-bottom: 3px;
    line-height: 1.3;
}

/* Server层特殊样式 */
.server-layer {
    border-left: 5px solid #9C27B0;
}

.server-resources {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.resource-item {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.resource-item:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateX(3px);
}

.resource-item.active {
    background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
    color: white;
    transform: scale(1.02);
}

.resource-icon {
    font-size: 1.2em;
}

.resource-info {
    flex: 1;
}

.resource-info h6 {
    margin: 0 0 3px 0;
    color: #333;
    font-size: 0.9em;
}

.resource-item.active .resource-info h6 {
    color: white;
}

.resource-status {
    font-size: 0.8em;
    color: #4CAF50;
    font-weight: bold;
}

.resource-item.active .resource-status {
    color: rgba(255, 255, 255, 0.9);
}

.server-log {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    flex: 1;
}

.server-log h6 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 0.9em;
}

/* 通信流样式 */
.communication-flow {
    display: flex;
    align-items: center;
    justify-content: center;
}

.arrow-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    height: 80px;
}

.arrow-line {
    width: 60px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.arrow-head {
    font-size: 1.8em;
    color: #667eea;
    font-weight: bold;
    margin-top: -8px;
    transition: all 0.3s ease;
}

.arrow-container.active .arrow-line {
    background: #4CAF50;
    animation: flow 1s infinite;
}

.arrow-container.active .arrow-head {
    color: #4CAF50;
    animation: bounce 0.5s infinite alternate;
}

.message-bubble {
    position: absolute;
    top: -35px;
    background: #667eea;
    color: white;
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 0.8em;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
    max-width: 140px;
    text-align: center;
}

.message-bubble.show {
    opacity: 1;
    transform: translateY(0);
}

.message-bubble::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #667eea;
}

/* 返回流样式 */
.return-flow {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.return-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 60px;
}

.return-arrow .arrow-line {
    width: 60px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
}

.return-arrow .arrow-head {
    font-size: 1.8em;
    color: #667eea;
    font-weight: bold;
    margin-top: -8px;
}

.return-arrow.active .arrow-line {
    background: #4CAF50;
    animation: flow-reverse 1s infinite;
}

.return-arrow.active .arrow-head {
    color: #4CAF50;
    animation: bounce 0.5s infinite alternate;
}

.return-arrow .message-bubble {
    top: 35px;
}

.return-arrow .message-bubble::after {
    top: -10px;
    border-top-color: transparent;
    border-bottom-color: #667eea;
}

/* 流程步骤样式 */
.flow-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.flow-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
    text-align: center;
}

.flow-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.flow-step {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 3px solid transparent;
}

.flow-step.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.step-number {
    background: #667eea;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-weight: bold;
    font-size: 1.2em;
}

.flow-step.active .step-number {
    background: rgba(255, 255, 255, 0.3);
}

.step-content h5 {
    margin-bottom: 8px;
    color: #667eea;
}

.flow-step.active .step-content h5 {
    color: white;
}

.step-content p {
    font-size: 0.9em;
    opacity: 0.8;
}

/* 监控面板样式 */
.monitoring-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.monitoring-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
    text-align: center;
}

.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.monitor-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.monitor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.monitor-icon {
    font-size: 2em;
    color: #667eea;
}

.monitor-info h5 {
    margin: 0 0 5px 0;
    color: #667eea;
    font-size: 1em;
}

.monitor-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #333;
}

/* 知识点区域样式 */
.knowledge-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.knowledge-section h3 {
    color: #667eea;
    margin-bottom: 25px;
    font-size: 1.4em;
    text-align: center;
}

.knowledge-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.knowledge-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    transition: transform 0.3s ease;
}

.knowledge-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.knowledge-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.knowledge-icon {
    font-size: 1.5em;
    margin-right: 12px;
}

.knowledge-card h4 {
    color: #667eea;
    margin: 0;
    font-size: 1.1em;
}

.knowledge-content p {
    margin-bottom: 10px;
    line-height: 1.5;
    font-size: 0.95em;
}

.knowledge-content ul {
    margin: 10px 0 10px 20px;
}

.knowledge-content li {
    margin-bottom: 5px;
    font-size: 0.9em;
}

.knowledge-content p strong {
    color: #667eea;
}

/* 代码展示区域 */
.code-section {
    text-align: center;
    margin-bottom: 30px;
}

.code-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.code-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 3% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 900px;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #667eea;
}

.modal-content h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.4em;
}

.modal-content pre {
    background: #1a1a1a;
    color: #f8f8f2;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

@keyframes flow {
    0% {
        background-position: 0% 50%;
    }

    100% {
        background-position: 100% 50%;
    }
}

@keyframes flow-reverse {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes bounce {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-5px);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease;
}

/* 成功和错误消息样式 */
.success-message {
    background: #e8f5e8;
    color: #4CAF50;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

.success-message h6 {
    margin: 0 0 10px 0;
    font-size: 1.1em;
}

.success-message p {
    margin: 5px 0;
    font-size: 0.9em;
}

.error-message {
    background: #ffebee;
    color: #f44336;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #f44336;
}

.error-message h6 {
    margin: 0 0 10px 0;
    font-size: 1.1em;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .architecture-container {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .communication-flow {
        transform: rotate(90deg);
        height: 60px;
        width: 100px;
        margin: 10px auto;
    }

    .return-flow {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .return-arrow {
        transform: rotate(-90deg);
        margin: 10px auto;
    }

    .message-bubble {
        transform: rotate(-90deg);
        top: -20px;
        left: -70px;
    }

    .return-arrow .message-bubble {
        transform: rotate(90deg);
        top: -20px;
        left: -70px;
    }
}

@media (max-width: 768px) {
    .knowledge-grid {
        grid-template-columns: 1fr;
    }

    .monitoring-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .flow-steps {
        grid-template-columns: 1fr;
    }

    .header h1 {
        font-size: 2em;
    }

    .container {
        padding: 10px;
    }

    .layer {
        min-height: auto;
        padding: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .monitoring-grid {
        grid-template-columns: 1fr;
    }

    .request-form {
        padding: 10px;
    }

    .function-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .monitor-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}