/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 0;
}

/* 概念说明区域 */
.concept-section {
    margin-bottom: 30px;
}

.concept-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #667eea;
}

.concept-card h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.concept-card p {
    line-height: 1.6;
    margin-bottom: 15px;
}

.analogy {
    background: #f8f9ff;
    padding: 15px;
    border-radius: 10px;
    border-left: 3px solid #667eea;
    margin-top: 15px;
}

.analogy-icon {
    font-size: 1.5em;
    margin-right: 10px;
}

/* 演示区域 */
.demo-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 插件库样式 */
.plugin-library {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.plugin-library h3 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.library-desc {
    color: #666;
    margin-bottom: 20px;
    font-style: italic;
}

.plugins-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.plugin {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 12px;
    cursor: grab;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.plugin:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.plugin:active {
    cursor: grabbing;
    transform: scale(0.95);
}

.plugin-icon {
    font-size: 2em;
    text-align: center;
    margin-bottom: 10px;
}

.plugin-name {
    font-weight: bold;
    text-align: center;
    margin-bottom: 8px;
    color: #333;
}

.plugin-desc {
    font-size: 0.9em;
    color: #666;
    text-align: center;
    margin-bottom: 8px;
}

.plugin-version {
    font-size: 0.8em;
    color: #999;
    text-align: center;
    background: rgba(255, 255, 255, 0.7);
    padding: 2px 8px;
    border-radius: 10px;
    display: inline-block;
    width: 100%;
    text-align: center;
}

/* 主系统样式 */
.main-system {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.main-system h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.system-architecture {
    background: #f8f9ff;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.core-layer h4 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1em;
}

.architecture-item {
    background: white;
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 6px;
    font-size: 0.9em;
    border-left: 3px solid #667eea;
}

.system-core {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 20px;
    background: #fafbff;
}

.core-status {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background: white;
    border-radius: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ccc;
    margin-right: 10px;
    animation: pulse 2s infinite;
}

.status-indicator.active {
    background: #4CAF50;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.drop-zone {
    min-height: 150px;
    border: 3px dashed #667eea;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    background: rgba(102, 126, 234, 0.05);
}

.drop-zone.drag-over {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    transform: scale(1.02);
}

.drop-zone-content {
    text-align: center;
}

.drop-icon {
    font-size: 3em;
    margin-bottom: 10px;
    opacity: 0.7;
}

.drop-hint {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}

.installed-plugins h4 {
    color: #667eea;
    margin-bottom: 15px;
}

.plugin-list {
    min-height: 50px;
}

.empty-state {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.installed-plugin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
    margin: 8px 0;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.plugin-info {
    display: flex;
    align-items: center;
}

.plugin-info span {
    margin-left: 10px;
}

.uninstall-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.uninstall-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 测试区域样式 */
.test-area {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.test-area h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.test-tabs {
    width: 100%;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid #eee;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-size: 1em;
    color: #666;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.tab-content {
    min-height: 200px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.system-overview {
    padding: 20px;
    background: #f8f9ff;
    border-radius: 10px;
}

.capability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.capability-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.capability-icon {
    font-size: 1.5em;
    margin-right: 12px;
}

.capability-item span:nth-child(2) {
    flex: 1;
    font-weight: 500;
}

.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.status.enabled {
    background: #e8f5e8;
    color: #4CAF50;
}

.status.disabled {
    background: #ffeaa7;
    color: #e17055;
}

.overview-hint {
    color: #666;
    font-style: italic;
    text-align: center;
    margin-top: 15px;
}

/* 系统日志样式 */
.system-log {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.system-log h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.log-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.log-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background 0.3s ease;
}

.log-btn:hover {
    background: #5a6fd8;
}

.log-count {
    font-size: 0.9em;
    color: #666;
}

.log-content {
    background: #1a1a1a;
    color: #00ff00;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 5px;
    display: flex;
    align-items: flex-start;
}

.log-time {
    color: #888;
    margin-right: 8px;
    min-width: 80px;
}

.log-type {
    margin-right: 8px;
    min-width: 80px;
    font-weight: bold;
}

.log-type:contains("SYSTEM") {
    color: #00bfff;
}

.log-type:contains("PLUGIN") {
    color: #ffa500;
}

.log-type:contains("ERROR") {
    color: #ff4444;
}

.log-message {
    flex: 1;
}

/* 知识点区域样式 */
.knowledge-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.knowledge-section h3 {
    color: #667eea;
    margin-bottom: 25px;
    font-size: 1.4em;
    text-align: center;
}

.knowledge-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.knowledge-item {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    transition: transform 0.3s ease;
}

.knowledge-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.knowledge-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.knowledge-icon {
    font-size: 1.5em;
    margin-right: 12px;
}

.knowledge-item h4 {
    color: #667eea;
    margin: 0;
    font-size: 1.1em;
}

.knowledge-item p {
    margin-bottom: 8px;
    line-height: 1.5;
    font-size: 0.95em;
}

.knowledge-item p strong {
    color: #667eea;
}

/* 代码展示区域 */
.code-section {
    text-align: center;
    margin-bottom: 30px;
}

.code-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.code-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #667eea;
}

.modal-content h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.4em;
}

.modal-content pre {
    background: #1a1a1a;
    color: #f8f8f2;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .demo-area {
        grid-template-columns: 1fr;
    }

    .plugins-container {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .knowledge-grid {
        grid-template-columns: 1fr;
    }

    .capability-grid {
        grid-template-columns: 1fr;
    }

    .header h1 {
        font-size: 2em;
    }

    .container {
        padding: 10px;
    }
}