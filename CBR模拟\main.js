// CBR基于案例推理演示 - 主要逻辑

class CBRSystem {
    constructor() {
        this.caseLibrary = new Map();
        this.currentScenario = null;
        this.currentProblem = null;
        this.currentStep = 0;
        this.totalCases = 0;
        this.successCases = 0;
        this.initializeSystem();
        this.setupEventListeners();
    }

    initializeSystem() {
        // 初始化不同场景的案例库
        this.initializeCaseLibraries();
        this.updateProgressStats();
    }

    initializeCaseLibraries() {
        // 医疗诊断案例
        this.caseLibrary.set('medical', [
            {
                id: 'med_001',
                title: '发热咳嗽案例',
                symptoms: ['发热', '咳嗽', '乏力'],
                diagnosis: '上呼吸道感染',
                treatment: '休息、多饮水、对症治疗',
                outcome: 'success'
            },
            {
                id: 'med_002',
                title: '胸痛气短案例',
                symptoms: ['胸痛', '气短', '心悸'],
                diagnosis: '心律不齐',
                treatment: '心电图检查、药物治疗',
                outcome: 'success'
            },
            {
                id: 'med_003',
                title: '头痛眩晕案例',
                symptoms: ['头痛', '眩晕', '恶心'],
                diagnosis: '偏头痛',
                treatment: '止痛药物、避免诱因',
                outcome: 'partial'
            }
        ]);

        // 法律咨询案例
        this.caseLibrary.set('legal', [
            {
                id: 'law_001',
                title: '合同纠纷案例',
                type: '民事纠纷',
                details: '买卖合同违约',
                solution: '协商调解、违约金赔偿',
                outcome: 'success'
            },
            {
                id: 'law_002',
                title: '劳动争议案例',
                type: '劳动纠纷',
                details: '工资拖欠问题',
                solution: '劳动仲裁、法律援助',
                outcome: 'success'
            },
            {
                id: 'law_003',
                title: '知识产权案例',
                type: '知识产权',
                details: '商标侵权纠纷',
                solution: '停止侵权、损害赔偿',
                outcome: 'partial'
            }
        ]);

        // 技术支持案例
        this.caseLibrary.set('technical', [
            {
                id: 'tech_001',
                title: '网络连接故障',
                problem: '无法上网',
                symptoms: ['网络断开', 'DNS错误'],
                solution: '重启路由器、检查网线',
                outcome: 'success'
            },
            {
                id: 'tech_002',
                title: '软件崩溃问题',
                problem: '程序异常退出',
                symptoms: ['蓝屏', '内存错误'],
                solution: '更新驱动、重装软件',
                outcome: 'success'
            },
            {
                id: 'tech_003',
                title: '打印机故障',
                problem: '无法打印',
                symptoms: ['卡纸', '墨盒问题'],
                solution: '清理卡纸、更换墨盒',
                outcome: 'partial'
            }
        ]);

        // 教育推荐案例
        this.caseLibrary.set('education', [
            {
                id: 'edu_001',
                title: '编程学习路径',
                background: '计算机专业',
                goal: '成为全栈开发者',
                recommendation: 'JavaScript → React → Node.js',
                outcome: 'success'
            },
            {
                id: 'edu_002',
                title: '数据科学路径',
                background: '数学专业',
                goal: '数据分析师',
                recommendation: 'Python → Pandas → Machine Learning',
                outcome: 'success'
            },
            {
                id: 'edu_003',
                title: '设计学习路径',
                background: '艺术专业',
                goal: 'UI/UX设计师',
                recommendation: 'Photoshop → Figma → 用户体验',
                outcome: 'partial'
            }
        ]);
    }

    setupEventListeners() {
        const scenarioSelect = document.getElementById('scenarioSelect');
        const startBtn = document.getElementById('startCBR');

        scenarioSelect.addEventListener('change', (e) => {
            this.selectScenario(e.target.value);
        });

        startBtn.addEventListener('click', () => {
            this.startCBRProcess();
        });
    }

    selectScenario(scenarioType) {
        if (!scenarioType) {
            this.currentScenario = null;
            this.updateProblemDetails('请先选择一个问题场景');
            this.updateCaseLibraryDisplay([]);
            document.getElementById('startCBR').disabled = true;
            return;
        }

        this.currentScenario = scenarioType;
        this.generateNewProblem(scenarioType);
        this.updateCaseLibraryDisplay(this.caseLibrary.get(scenarioType));
        document.getElementById('startCBR').disabled = false;
    }

    generateNewProblem(scenarioType) {
        const problems = {
            medical: {
                title: '新患者症状分析',
                description: '患者主诉：持续发热3天，伴有咳嗽和轻微胸痛，体温38.5°C',
                symptoms: ['发热', '咳嗽', '胸痛'],
                context: '需要根据症状进行初步诊断'
            },
            legal: {
                title: '新法律咨询案件',
                description: '客户咨询：签订的装修合同中，装修公司未按约定时间完工，且质量不达标',
                type: '合同纠纷',
                context: '需要提供法律建议和解决方案'
            },
            technical: {
                title: '新技术故障报告',
                description: '用户反馈：电脑开机后显示蓝屏，错误代码0x0000007B',
                symptoms: ['蓝屏', '启动失败', '系统错误'],
                context: '需要诊断问题并提供解决方案'
            },
            education: {
                title: '新学习路径咨询',
                description: '学员背景：市场营销专业毕业，希望转行进入人工智能领域',
                background: '市场营销专业',
                goal: 'AI工程师',
                context: '需要制定个性化学习计划'
            }
        };

        this.currentProblem = problems[scenarioType];
        this.updateProblemDetails(this.formatProblemDescription(this.currentProblem));
    }

    formatProblemDescription(problem) {
        let html = `
            <div class="problem-card">
                <h4>${problem.title}</h4>
                <p><strong>问题描述：</strong>${problem.description}</p>
        `;

        if (problem.symptoms) {
            html += `<p><strong>关键特征：</strong>${problem.symptoms.join('、')}</p>`;
        }
        if (problem.type) {
            html += `<p><strong>案件类型：</strong>${problem.type}</p>`;
        }
        if (problem.background) {
            html += `<p><strong>学员背景：</strong>${problem.background}</p>`;
        }
        if (problem.goal) {
            html += `<p><strong>目标职位：</strong>${problem.goal}</p>`;
        }

        html += `<p><strong>任务：</strong>${problem.context}</p></div>`;
        return html;
    }

    updateProblemDetails(content) {
        document.getElementById('problemDetails').innerHTML = content;
    }

    updateCaseLibraryDisplay(cases) {
        const container = document.getElementById('casesContainer');
        const caseCount = document.getElementById('caseCount');

        if (!cases || cases.length === 0) {
            container.innerHTML = '<div class="empty-state">案例库为空，请先选择场景</div>';
            caseCount.textContent = '0';
            return;
        }

        caseCount.textContent = cases.length;
        
        let html = '';
        cases.forEach(case_ => {
            html += `
                <div class="case-item" data-case-id="${case_.id}">
                    <div class="case-title">${case_.title}</div>
                    <div class="case-details">${this.formatCaseDetails(case_)}</div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    formatCaseDetails(case_) {
        if (case_.symptoms) {
            return `症状：${case_.symptoms.join('、')}`;
        }
        if (case_.type) {
            return `类型：${case_.type}`;
        }
        if (case_.problem) {
            return `问题：${case_.problem}`;
        }
        if (case_.background) {
            return `背景：${case_.background} → ${case_.goal}`;
        }
        return '详细信息';
    }

    async startCBRProcess() {
        if (!this.currentScenario || !this.currentProblem) {
            alert('请先选择场景');
            return;
        }

        // 显示推理区域
        document.getElementById('reasoningArea').style.display = 'block';
        document.getElementById('reasoningArea').scrollIntoView({ behavior: 'smooth' });

        // 重置步骤状态
        this.resetProcessSteps();
        this.currentStep = 0;

        // 开始四步CBR流程
        await this.executeStep1_Retrieve();
        await this.executeStep2_Reuse();
        await this.executeStep3_Revise();
        await this.executeStep4_Retain();
    }

    resetProcessSteps() {
        for (let i = 1; i <= 4; i++) {
            const step = document.getElementById(`step${i}`);
            step.classList.remove('active', 'completed');
            step.querySelector('.step-status').textContent = '等待开始';
        }

        // 隐藏所有显示区域
        document.getElementById('similarityDisplay').style.display = 'none';
        document.getElementById('solutionDisplay').style.display = 'none';
        document.getElementById('feedbackArea').style.display = 'none';
    }

    async executeStep1_Retrieve() {
        this.currentStep = 1;
        this.updateStepStatus(1, 'active', '正在检索相似案例...');
        this.updateCurrentStepDisplay('🔍 检索 (Retrieve)', '正在从案例库中搜索与当前问题最相似的历史案例...');

        // 模拟检索过程
        await this.delay(2000);

        // 计算相似度
        const cases = this.caseLibrary.get(this.currentScenario);
        const similarities = this.calculateSimilarities(cases);

        // 显示相似度结果
        this.displaySimilarities(similarities);
        
        this.updateStepStatus(1, 'completed', '检索完成');
        await this.delay(1000);
    }

    calculateSimilarities(cases) {
        // 模拟相似度计算
        return cases.map(case_ => ({
            case: case_,
            similarity: Math.random() * 0.4 + 0.6 // 60%-100%的相似度
        })).sort((a, b) => b.similarity - a.similarity);
    }

    displaySimilarities(similarities) {
        const display = document.getElementById('similarityDisplay');
        const list = document.getElementById('similarityList');

        let html = '';
        similarities.forEach((item, index) => {
            const percentage = Math.round(item.similarity * 100);
            html += `
                <div class="similarity-item ${index === 0 ? 'highlight-animation' : ''}">
                    <div>
                        <strong>${item.case.title}</strong><br>
                        <small>${this.formatCaseDetails(item.case)}</small>
                    </div>
                    <div class="similarity-score">${percentage}%</div>
                </div>
            `;
        });

        list.innerHTML = html;
        display.style.display = 'block';
        display.classList.add('slide-in');

        // 高亮最相似的案例
        this.highlightBestMatch(similarities[0].case.id);
    }

    highlightBestMatch(caseId) {
        // 移除之前的高亮
        document.querySelectorAll('.case-item').forEach(item => {
            item.classList.remove('highlighted');
        });

        // 高亮最匹配的案例
        const caseElement = document.querySelector(`[data-case-id="${caseId}"]`);
        if (caseElement) {
            caseElement.classList.add('highlighted');
            caseElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    async executeStep2_Reuse() {
        this.currentStep = 2;
        this.updateStepStatus(2, 'active', '正在重用案例...');
        this.updateCurrentStepDisplay('🔄 重用 (Reuse)', '正在将最相似案例的解决方案适配到当前问题...');

        await this.delay(2000);

        // 生成适配后的解决方案
        const solution = this.generateAdaptedSolution();
        this.displaySolution(solution);

        this.updateStepStatus(2, 'completed', '重用完成');
        await this.delay(1000);
    }

    generateAdaptedSolution() {
        const solutions = {
            medical: {
                diagnosis: '初步诊断：上呼吸道感染伴轻微胸部不适',
                treatment: '建议治疗方案：\n1. 休息，多饮水\n2. 对症治疗（退热、止咳）\n3. 观察病情变化\n4. 必要时复查',
                confidence: '85%'
            },
            legal: {
                analysis: '案件分析：装修合同违约纠纷',
                solution: '建议解决方案：\n1. 收集违约证据（合同、照片、沟通记录）\n2. 先行协商调解\n3. 要求违约金和质量整改\n4. 必要时申请仲裁或诉讼',
                confidence: '90%'
            },
            technical: {
                diagnosis: '问题诊断：硬盘驱动器故障或系统文件损坏',
                solution: '解决方案：\n1. 检查硬盘连接线\n2. 运行硬盘检测工具\n3. 尝试系统修复\n4. 备份数据后重装系统',
                confidence: '80%'
            },
            education: {
                analysis: '学习路径分析：市场营销转AI工程师',
                recommendation: '推荐学习路径：\n1. 数学基础（线性代数、概率统计）\n2. Python编程语言\n3. 机器学习基础\n4. 深度学习框架\n5. 实际项目经验',
                confidence: '88%'
            }
        };

        return solutions[this.currentScenario];
    }

    displaySolution(solution) {
        const display = document.getElementById('solutionDisplay');
        const content = document.getElementById('solutionContent');

        let html = '<div class="solution-card">';
        Object.keys(solution).forEach(key => {
            if (key !== 'confidence') {
                const label = this.getSolutionLabel(key);
                const value = solution[key].replace(/\n/g, '<br>');
                html += `<p><strong>${label}：</strong></p><p>${value}</p>`;
            }
        });
        html += `<div class="confidence-score">置信度：${solution.confidence}</div></div>`;

        content.innerHTML = html;
        display.style.display = 'block';
        display.classList.add('slide-in');
    }

    getSolutionLabel(key) {
        const labels = {
            diagnosis: '诊断结果',
            treatment: '治疗方案',
            analysis: '案件分析',
            solution: '解决方案',
            recommendation: '学习建议'
        };
        return labels[key] || key;
    }

    async executeStep3_Revise() {
        this.currentStep = 3;
        this.updateStepStatus(3, 'active', '等待反馈...');
        this.updateCurrentStepDisplay('⚙️ 修正 (Revise)', '请对解决方案进行评估，系统将根据反馈进行优化...');

        // 显示反馈区域
        const feedbackArea = document.getElementById('feedbackArea');
        feedbackArea.style.display = 'block';
        feedbackArea.classList.add('slide-in');

        // 等待用户反馈（通过全局函数处理）
        return new Promise(resolve => {
            window.cbrResolveStep3 = resolve;
        });
    }

    async executeStep4_Retain() {
        this.currentStep = 4;
        this.updateStepStatus(4, 'active', '正在保留案例...');
        this.updateCurrentStepDisplay('💾 保留 (Retain)', '正在将新案例和解决方案存储到案例库中...');

        await this.delay(2000);

        // 将新案例添加到案例库
        this.addNewCaseToLibrary();
        
        // 更新统计信息
        this.updateProgressStats();

        this.updateStepStatus(4, 'completed', '保留完成');
        
        // 显示完成信息
        this.updateCurrentStepDisplay('✅ CBR流程完成', 'CBR四步流程已完成！系统通过这次经验变得更加智能。');
        
        await this.delay(1000);
        this.showCompletionMessage();
    }

    addNewCaseToLibrary() {
        const cases = this.caseLibrary.get(this.currentScenario);
        const newCase = this.createNewCase();
        cases.push(newCase);
        
        this.totalCases++;
        this.updateCaseLibraryDisplay(cases);
    }

    createNewCase() {
        const caseTemplates = {
            medical: {
                id: `med_${Date.now()}`,
                title: '新诊断案例',
                symptoms: this.currentProblem.symptoms,
                diagnosis: '基于CBR推理的诊断',
                treatment: '适配的治疗方案',
                outcome: 'pending'
            },
            legal: {
                id: `law_${Date.now()}`,
                title: '新法律案例',
                type: this.currentProblem.type,
                details: '基于CBR分析的案件',
                solution: '适配的解决方案',
                outcome: 'pending'
            },
            technical: {
                id: `tech_${Date.now()}`,
                title: '新技术案例',
                problem: '基于CBR诊断的问题',
                symptoms: this.currentProblem.symptoms,
                solution: '适配的解决方案',
                outcome: 'pending'
            },
            education: {
                id: `edu_${Date.now()}`,
                title: '新学习案例',
                background: this.currentProblem.background,
                goal: this.currentProblem.goal,
                recommendation: '基于CBR的学习路径',
                outcome: 'pending'
            }
        };

        return caseTemplates[this.currentScenario];
    }

    updateStepStatus(stepNumber, status, statusText) {
        const step = document.getElementById(`step${stepNumber}`);
        
        // 移除之前的状态
        step.classList.remove('active', 'completed');
        
        // 添加新状态
        if (status !== 'waiting') {
            step.classList.add(status);
        }
        
        // 更新状态文本
        step.querySelector('.step-status').textContent = statusText;
    }

    updateCurrentStepDisplay(title, content) {
        const stepHeader = document.querySelector('#currentStep .step-name');
        const stepContent = document.querySelector('#currentStep .step-content');
        
        stepHeader.textContent = title;
        stepContent.innerHTML = `<p>${content}</p>`;
    }

    updateProgressStats() {
        document.getElementById('totalCases').textContent = this.totalCases;
        document.getElementById('successCases').textContent = this.successCases;
        
        const accuracy = this.totalCases > 0 ? Math.round((this.successCases / this.totalCases) * 100) : 0;
        document.getElementById('accuracyRate').textContent = `${accuracy}%`;
        
        const progress = Math.min((this.totalCases / 20) * 100, 100); // 假设20个案例为满进度
        document.getElementById('progressFill').style.width = `${progress}%`;
    }

    showCompletionMessage() {
        const content = document.querySelector('#currentStep .step-content');
        content.innerHTML = `
            <div class="completion-message">
                <h4>🎉 恭喜！CBR学习循环完成</h4>
                <p>通过这次CBR推理过程，你体验了：</p>
                <ul>
                    <li>✅ <strong>检索</strong>：从案例库找到相似经验</li>
                    <li>✅ <strong>重用</strong>：适配历史方案解决新问题</li>
                    <li>✅ <strong>修正</strong>：根据反馈优化解决方案</li>
                    <li>✅ <strong>保留</strong>：积累新经验增强系统智能</li>
                </ul>
                <p>系统现在拥有了新的案例，下次遇到类似问题时会更加智能！</p>
                <button class="start-btn" onclick="location.reload()">开始新的CBR演示</button>
            </div>
        `;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 全局函数
function provideFeedback(feedbackType) {
    const feedbackResult = document.getElementById('feedbackResult');
    const cbrSystem = window.cbrSystem;
    
    let message = '';
    let nextAction = '';
    
    switch (feedbackType) {
        case 'success':
            message = '✅ 解决方案有效！系统将保留这个成功案例。';
            nextAction = '系统学习：将此案例标记为成功经验';
            cbrSystem.successCases++;
            break;
        case 'partial':
            message = '⚠️ 解决方案部分有效，需要进一步优化。';
            nextAction = '系统学习：记录需要改进的方面';
            break;
        case 'failure':
            message = '❌ 解决方案无效，需要重新分析。';
            nextAction = '系统学习：分析失败原因，避免类似错误';
            break;
    }
    
    feedbackResult.innerHTML = `
        <div class="feedback-message">
            <p>${message}</p>
            <p><strong>系统响应：</strong>${nextAction}</p>
        </div>
    `;
    
    // 继续到下一步
    setTimeout(() => {
        cbrSystem.updateStepStatus(3, 'completed', '修正完成');
        if (window.cbrResolveStep3) {
            window.cbrResolveStep3();
        }
    }, 2000);
}

function showCoreCode() {
    const modal = document.getElementById('codeModal');
    const codeDisplay = document.getElementById('codeDisplay');
    
    codeDisplay.textContent = getCoreCode();
    modal.style.display = 'block';
}

function closeCoreCode() {
    const modal = document.getElementById('codeModal');
    modal.style.display = 'none';
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('codeModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// 初始化系统
let cbrSystem;

document.addEventListener('DOMContentLoaded', function() {
    cbrSystem = new CBRSystem();
    window.cbrSystem = cbrSystem; // 供全局函数访问
});
