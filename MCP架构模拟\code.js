// 核心代码展示 - MCP三层架构实现

function getCoreCode() {
    return `
// ===== MCP模型上下文协议三层架构核心实现代码 =====

// 1. MCP协议基础接口定义
interface MCPMessage {
    jsonrpc: "2.0";
    id?: string | number;
    method?: string;
    params?: any;
    result?: any;
    error?: MCPError;
}

interface MCPError {
    code: number;
    message: string;
    data?: any;
}

// 2. Host层实现 - 用户控制起点
class MCPHost {
    private client: MCPClient;
    private sessionId: string;
    
    constructor(client: MCPClient) {
        this.client = client;
        this.sessionId = this.generateSessionId();
    }
    
    // 用户操作入口
    async handleUserRequest(userInput: any): Promise<any> {
        try {
            console.log('Host: 接收用户请求', userInput);
            
            // 1. 验证用户输入
            const validatedInput = this.validateUserInput(userInput);
            
            // 2. 构造MCP请求
            const mcpRequest: MCPMessage = {
                jsonrpc: "2.0",
                id: this.generateRequestId(),
                method: "processUserRequest",
                params: {
                    sessionId: this.sessionId,
                    userInput: validatedInput,
                    timestamp: Date.now()
                }
            };
            
            // 3. 发送到Client层
            const response = await this.client.handleRequest(mcpRequest);
            
            // 4. 处理响应并展示给用户
            return this.formatResponseForUser(response);
            
        } catch (error) {
            console.error('Host: 请求处理失败', error);
            return this.handleError(error);
        }
    }
    
    // 验证用户输入
    private validateUserInput(input: any): any {
        if (!input || typeof input !== 'object') {
            throw new Error('无效的用户输入');
        }
        
        // 输入清理和验证逻辑
        return {
            ...input,
            sanitized: true,
            validatedAt: Date.now()
        };
    }
    
    // 格式化响应给用户
    private formatResponseForUser(response: MCPMessage): any {
        if (response.error) {
            return {
                success: false,
                message: response.error.message,
                errorCode: response.error.code
            };
        }
        
        return {
            success: true,
            data: response.result,
            message: '请求处理成功'
        };
    }
    
    private generateSessionId(): string {
        return 'host_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    private generateRequestId(): string {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}

// 3. Client层实现 - 协议守门人
class MCPClient {
    private servers: Map<string, MCPServer> = new Map();
    private authManager: AuthManager;
    private protocolConverter: ProtocolConverter;
    private requestRouter: RequestRouter;
    
    constructor() {
        this.authManager = new AuthManager();
        this.protocolConverter = new ProtocolConverter();
        this.requestRouter = new RequestRouter();
    }
    
    // 处理来自Host的请求
    async handleRequest(request: MCPMessage): Promise<MCPMessage> {
        try {
            console.log('Client: 接收Host请求', request);
            
            // 1. 安全验证
            const authResult = await this.authManager.authenticate(request);
            if (!authResult.success) {
                return this.createErrorResponse(request.id, -32001, '身份验证失败');
            }
            
            // 2. 协议转换
            const convertedRequest = this.protocolConverter.convertRequest(request);
            
            // 3. 请求路由
            const targetServer = this.requestRouter.route(convertedRequest);
            if (!targetServer) {
                return this.createErrorResponse(request.id, -32002, '无可用服务器');
            }
            
            // 4. 转发到Server层
            const serverResponse = await targetServer.processRequest(convertedRequest);
            
            // 5. 响应转换
            const convertedResponse = this.protocolConverter.convertResponse(serverResponse);
            
            console.log('Client: 返回响应给Host', convertedResponse);
            return convertedResponse;
            
        } catch (error) {
            console.error('Client: 请求处理失败', error);
            return this.createErrorResponse(request.id, -32000, '内部服务器错误');
        }
    }
    
    // 注册Server
    registerServer(serverId: string, server: MCPServer): void {
        this.servers.set(serverId, server);
        console.log(\`Client: 注册Server - \${serverId}\`);
    }
    
    // 创建错误响应
    private createErrorResponse(id: any, code: number, message: string): MCPMessage {
        return {
            jsonrpc: "2.0",
            id: id,
            error: {
                code: code,
                message: message
            }
        };
    }
}

// 4. Server层实现 - 服务调度中心
class MCPServer {
    private serverId: string;
    private resources: Map<string, any> = new Map();
    private tools: Map<string, Function> = new Map();
    private prompts: Map<string, any> = new Map();
    
    constructor(serverId: string) {
        this.serverId = serverId;
        this.initializeResources();
        this.registerTools();
        this.loadPrompts();
    }
    
    // 处理来自Client的请求
    async processRequest(request: MCPMessage): Promise<MCPMessage> {
        try {
            console.log(\`Server[\${this.serverId}]: 处理请求\`, request);
            
            const { method, params } = request;
            
            switch (method) {
                case 'processUserRequest':
                    return await this.handleUserRequest(request.id, params);
                case 'getResources':
                    return await this.getResources(request.id);
                case 'callTool':
                    return await this.callTool(request.id, params);
                case 'getPrompts':
                    return await this.getPrompts(request.id);
                default:
                    return this.createErrorResponse(request.id, -32601, '方法未找到');
            }
            
        } catch (error) {
            console.error(\`Server[\${this.serverId}]: 处理失败\`, error);
            return this.createErrorResponse(request.id, -32000, '服务器内部错误');
        }
    }
    
    // 处理用户请求
    private async handleUserRequest(id: any, params: any): Promise<MCPMessage> {
        const { userInput } = params;
        
        // 模拟业务逻辑处理
        const result = await this.executeBusinessLogic(userInput);
        
        return {
            jsonrpc: "2.0",
            id: id,
            result: {
                success: true,
                data: result,
                processedBy: this.serverId,
                timestamp: Date.now()
            }
        };
    }
    
    // 执行业务逻辑
    private async executeBusinessLogic(input: any): Promise<any> {
        // 模拟异步处理
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 根据输入类型调用不同的处理逻辑
        if (input.type === 'weather') {
            return this.processWeatherRequest(input);
        } else if (input.type === 'translate') {
            return this.processTranslateRequest(input);
        } else if (input.type === 'calculate') {
            return this.processCalculateRequest(input);
        } else if (input.type === 'search') {
            return this.processSearchRequest(input);
        } else {
            return this.processGeneral(input);
        }
    }
    
    // 天气查询处理
    private processWeatherRequest(input: any): any {
        return {
            type: 'weather',
            query: input.content,
            result: '北京今日天气：晴天，温度25°C，湿度65%，风力3级',
            timestamp: Date.now()
        };
    }
    
    // 翻译处理
    private processTranslateRequest(input: any): any {
        const translations = {
            '你好': 'Hello',
            '谢谢': 'Thank you',
            '再见': 'Goodbye'
        };
        
        const result = translations[input.content] || \`Translated: "\${input.content}"\`;
        
        return {
            type: 'translate',
            original: input.content,
            translated: result,
            timestamp: Date.now()
        };
    }
    
    // 计算处理
    private processCalculateRequest(input: any): any {
        try {
            const result = eval(input.content.replace(/[^0-9+\\-*/().\s]/g, ''));
            return {
                type: 'calculate',
                expression: input.content,
                result: result,
                timestamp: Date.now()
            };
        } catch (error) {
            return {
                type: 'calculate',
                expression: input.content,
                error: '计算表达式无效',
                timestamp: Date.now()
            };
        }
    }
    
    // 搜索处理
    private processSearchRequest(input: any): any {
        return {
            type: 'search',
            query: input.content,
            results: [
                \`关于"\${input.content}"的搜索结果\`,
                \`找到约 1,234,567 个相关结果\`
            ],
            timestamp: Date.now()
        };
    }
    
    // 通用处理
    private processGeneral(input: any): any {
        return {
            type: 'general',
            input: input,
            message: '请求已处理',
            timestamp: Date.now()
        };
    }
}

// ===== MCP协议的核心优势 =====

/*
1. 标准化接口
   - 统一的JSON-RPC协议
   - 跨平台兼容性
   - 易于集成和扩展

2. 三层架构优势
   - Host: 专注用户体验
   - Client: 处理协议和安全
   - Server: 提供核心服务

3. 安全性
   - 身份验证和授权
   - 请求验证和过滤
   - 错误处理和恢复

4. 可扩展性
   - 插件式Server架构
   - 动态服务发现
   - 负载均衡支持

5. 开发效率
   - 即插即用的组件
   - 标准化的开发流程
   - 丰富的工具生态
*/

// ===== 实际应用场景 =====

/*
1. AI助手应用
   - ChatGPT、Claude等对话界面
   - 工具调用和外部API集成
   - 多模态内容处理

2. 企业级AI平台
   - 统一的AI服务接入
   - 权限管理和审计
   - 服务编排和调度

3. 开发者工具
   - IDE插件和扩展
   - 代码生成和分析
   - 自动化测试和部署

4. 物联网和边缘计算
   - 设备管理和控制
   - 数据采集和处理
   - 实时监控和告警
*/
`;
}
