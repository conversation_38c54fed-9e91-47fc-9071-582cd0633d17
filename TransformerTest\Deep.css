/* 神经网络与深度学习模块样式 */

/* 新的神经网络可视化样式 */
.neuron-demo-container,
.perceptron-demo-container,
.multilayer-demo-container,
.backprop-demo-container {
    color: white;
    line-height: 1.6;
}

.demo-section {
    margin-bottom: 30px;
}

.demo-section h4,
.demo-section h5 {
    margin-bottom: 15px;
}

.demo-section h5 {
    color: #FFD700;
    font-size: 1.1em;
}

/* 神经元结构样式 */
.neuron-structure {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
}

.input-section,
.output-section {
    flex: 1;
    text-align: center;
}

.neuron-core {
    flex: 1;
    text-align: center;
    margin: 0 20px;
}

.input-item,
.bias-item {
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.weight-line {
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #4ECDC4, #FFD700);
    position: relative;
    margin: 0 10px;
}

.weight-label {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8em;
    color: #FFD700;
}

.neuron-body {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.sum-symbol,
.activation-func {
    font-size: 1.2em;
    font-weight: bold;
    color: white;
}

.computation-steps {
    margin-top: 15px;
}

.computation-steps .step {
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    margin: 5px 0;
    border-radius: 5px;
    font-size: 0.9em;
}

.output-value {
    font-size: 2em;
    font-weight: bold;
    color: #4ECDC4;
    margin: 10px 0;
}

.calculation-detail {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
}

.calc-step {
    margin: 10px 0;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
}

.activation-functions {
    margin: 20px 0;
}

.func-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.func-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

/* 感知器样式 */
.training-data {
    margin: 20px 0;
}

.data-table {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    font-weight: bold;
    text-align: center;
}

.table-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 8px 10px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-row.positive {
    background: rgba(76, 175, 80, 0.2);
}

.table-row.negative {
    background: rgba(244, 67, 54, 0.2);
}

.label.athlete {
    color: #4CAF50;
    font-weight: bold;
}

.label.normal {
    color: #FF5722;
    font-weight: bold;
}

.learning-steps {
    margin: 20px 0;
}

.step-item {
    display: flex;
    align-items: flex-start;
    margin: 15px 0;
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

/* 多层网络样式 */
.network-evolution {
    margin: 20px 0;
}

.evolution-comparison {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    gap: 20px;
}

.evolution-item {
    flex: 1;
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
}

.simple-network,
.complex-network {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15px 0;
}

.layer-simple {
    background: linear-gradient(45deg, #4ECDC4, #44A08D);
    padding: 10px 15px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    margin: 0 5px;
}

.arrow {
    font-size: 1.5em;
    color: #FFD700;
    margin: 0 10px;
}

.capability {
    margin-top: 10px;
    font-size: 0.9em;
    color: #FFD700;
}

/* XOR问题样式 */
.xor-explanation {
    display: flex;
    gap: 30px;
    margin: 20px 0;
}

.xor-table {
    flex: 1;
}

.xor-table table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    overflow: hidden;
}

.xor-table th,
.xor-table td {
    padding: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.xor-table th {
    background: rgba(255, 255, 255, 0.1);
    font-weight: bold;
}

.xor-solution {
    flex: 1;
}

.xor-network {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
}

.xor-layer {
    text-align: center;
}

.xor-title {
    font-size: 0.9em;
    color: #FFD700;
    margin-bottom: 10px;
}

.xor-node {
    background: linear-gradient(45deg, #667eea, #764ba2);
    padding: 8px 12px;
    border-radius: 6px;
    margin: 5px 0;
    font-size: 0.8em;
    color: white;
}

.xor-formula {
    text-align: center;
    margin-top: 15px;
    padding: 10px;
    background: rgba(255, 215, 0, 0.2);
    border-radius: 5px;
    color: #FFD700;
    font-weight: bold;
}

/* 特征层次化样式 */
.hierarchy-example {
    margin: 20px 0;
}

.hierarchy-title {
    text-align: center;
    font-weight: bold;
    margin-bottom: 20px;
    color: #FFD700;
}

.hierarchy-layers {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 15px;
}

.hierarchy-layer {
    text-align: center;
    flex: 1;
}

.layer-name {
    font-weight: bold;
    color: #4ECDC4;
    margin-bottom: 8px;
}

.layer-features {
    font-size: 0.9em;
    color: #FFD700;
    margin-bottom: 15px;
}

.feature-examples {
    display: flex;
    justify-content: center;
}

.pixel-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
    width: 30px;
    height: 30px;
}

.pixel {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.pixel.active {
    background: #4ECDC4;
}

.edge-patterns {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.edge {
    width: 20px;
    height: 3px;
    background: #FFD700;
}

.edge.vertical {
    width: 3px;
    height: 20px;
}

.edge.diagonal {
    transform: rotate(45deg);
}

.shape-patterns {
    display: flex;
    gap: 5px;
}

.shape {
    width: 15px;
    height: 15px;
    background: #FF6B6B;
}

.shape.circle {
    border-radius: 50%;
}

.shape.triangle {
    width: 0;
    height: 0;
    background: transparent;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 15px solid #FF6B6B;
}

.object-labels {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.label {
    background: linear-gradient(45deg, #4ECDC4, #44A08D);
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7em;
    color: white;
}

.hierarchy-arrow {
    font-size: 1.5em;
    color: #FFD700;
    margin: 0 10px;
}

/* 决策边界样式 */
.decision-boundary {
    margin: 20px 0;
}

.boundary-explanation {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
}

.boundary-formula {
    background: rgba(255, 215, 0, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: center;
    color: #FFD700;
}

.classification-rule {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.rule-item {
    flex: 1;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.positive-rule {
    background: rgba(76, 175, 80, 0.2);
    border: 2px solid #4CAF50;
}

.negative-rule {
    background: rgba(244, 67, 54, 0.2);
    border: 2px solid #FF5722;
}

/* 局限性样式 */
.limitations {
    margin: 20px 0;
}

.limitation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.limitation-item {
    background: rgba(255, 152, 0, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #FF9800;
}

/* 万能逼近定理样式 */
.universal-approximation {
    margin: 20px 0;
}

.theorem-explanation {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
}

.theorem-statement {
    background: rgba(33, 150, 243, 0.2);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #2196F3;
    margin-bottom: 15px;
}

.theorem-implications {
    display: flex;
    gap: 15px;
}

.implication {
    flex: 1;
    background: rgba(76, 175, 80, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

/* 反向传播演示样式 */
.backprop-overview {
    margin: 20px 0;
}

.algorithm-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.algo-step {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
}

.algo-step.forward {
    border-left: 4px solid #4ECDC4;
}

.algo-step.loss {
    border-left: 4px solid #FFD700;
}

.algo-step.backward {
    border-left: 4px solid #FF6B6B;
}

.algo-step.update {
    border-left: 4px solid #4CAF50;
}

.step-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.gradient-descent {
    margin: 30px 0;
}

.gradient-explanation {
    display: flex;
    gap: 30px;
    margin: 20px 0;
}

.mountain-analogy {
    flex: 1;
}

.mountain-visual {
    background: rgba(0, 0, 0, 0.3);
    height: 150px;
    border-radius: 10px;
    position: relative;
    margin: 15px 0;
    overflow: hidden;
}

.mountain-curve {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 50%, #4ECDC4 100%);
    border-radius: 50% 50% 0 0;
}

.ball {
    position: absolute;
    top: 20px;
    left: 20%;
    font-size: 1.5em;
    animation: rollDown 4s infinite;
}

@keyframes rollDown {
    0% {
        left: 20%;
        top: 20px;
    }

    50% {
        left: 60%;
        top: 60px;
    }

    100% {
        left: 80%;
        top: 80px;
    }
}

.valley {
    position: absolute;
    bottom: 10px;
    right: 20px;
    color: #FFD700;
    font-weight: bold;
    font-size: 0.9em;
}

.gradient-formula {
    flex: 1;
}

.formula-box {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 10px;
    margin: 15px 0;
}

.formula-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
    text-align: center;
}

.formula-explanation {
    margin-top: 15px;
}

.formula-explanation p {
    margin: 8px 0;
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.9);
}

.backprop-example {
    margin: 30px 0;
}

.calculation-flow {
    display: flex;
    gap: 30px;
    margin: 20px 0;
}

.calc-phase {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.calc-phase h6 {
    color: #FFD700;
    margin-bottom: 15px;
    text-align: center;
}

.calc-steps {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.calc-item {
    background: rgba(0, 0, 0, 0.2);
    padding: 8px 12px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.backprop-advantages {
    margin: 30px 0;
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.advantage-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #4ECDC4;
    transition: all 0.3s ease;
}

.advantage-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 神经元样式 */
.neuron {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4ECDC4, #44A08D);
    border: 3px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.8em;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.neuron:hover {
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.6);
}

.neuron.active {
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    box-shadow: 0 0 25px rgba(255, 107, 107, 0.8);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

/* 神经元大小变体 */
.neuron.large {
    width: 60px;
    height: 60px;
    font-size: 1.2em;
}

.neuron.small {
    width: 30px;
    height: 30px;
    font-size: 0.7em;
}

.neuron.tiny {
    width: 20px;
    height: 20px;
    font-size: 0.6em;
}

/* 网络容器样式 */
.network-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: space-around;
    align-items: center;
    min-height: 350px;
}

.layer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.layer-title {
    color: #FFD700;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}

/* 控制面板样式 */
.control-panel {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.control-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.control-btn.active {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
}

/* 信息面板样式 */
.info-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    backdrop-filter: blur(10px);
}

.info-panel h4 {
    color: #FFD700;
    margin-bottom: 10px;
}

.info-panel p {
    line-height: 1.6;
    opacity: 0.9;
}

/* 神经网络可视化样式 */
.neural-visualization {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    min-height: 400px;
    position: relative;
    overflow-y: auto;
    max-height: 500px;
}

/* 神经网络步骤样式 */
.neural-step {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.neural-step:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.neural-step.active {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateX(10px);
}

.neural-step h4 {
    margin-bottom: 8px;
    color: #FFD700;
}

.neural-step p {
    font-size: 0.9em;
    opacity: 0.9;
    line-height: 1.4;
}

/* 神经网络模态框样式 */
.neural-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    overflow: hidden;
}

.neural-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 2% auto;
    padding: 0;
    border-radius: 20px;
    width: 95%;
    max-width: 1400px;
    height: 90vh;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.neural-modal .close {
    color: white;
    float: right;
    font-size: 35px;
    font-weight: bold;
    position: absolute;
    right: 20px;
    top: 15px;
    z-index: 2001;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.neural-modal .close:hover {
    background: rgba(255, 0, 0, 0.6);
    transform: scale(1.1);
}

/* 神经网络演示区域样式 */
.neural-demo-area {
    height: 100%;
    display: flex;
    flex-direction: column;
    color: white;
    overflow: hidden;
}

.neural-header {
    text-align: center;
    padding: 30px 20px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.neural-header h2 {
    font-size: 2.2em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.neural-content {
    flex: 1;
    display: flex;
    padding: 20px;
    gap: 20px;
    overflow: hidden;
    min-height: 0;
}

.neural-sidebar {
    width: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    overflow-y: auto;
    max-height: calc(90vh - 200px);
}

.neural-main {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    overflow-y: auto;
    max-height: calc(90vh - 200px);
}