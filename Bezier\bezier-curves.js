// 全局变量
let currentCurve = 'linear';
let animationFrames = {};
let isDragging = false;
let dragPointIndex = -1;
let dragCurveType = '';

// 贝塞尔曲线数据
const curveData = {
    linear: {
        points: [
            { x: 100, y: 200 },
            { x: 500, y: 200 }
        ],
        animating: false,
        t: 0
    },
    quadratic: {
        points: [
            { x: 100, y: 300 },
            { x: 300, y: 100 },
            { x: 500, y: 300 }
        ],
        animating: false,
        t: 0
    },
    cubic: {
        points: [
            { x: 100, y: 300 },
            { x: 200, y: 100 },
            { x: 400, y: 100 },
            { x: 500, y: 300 }
        ],
        animating: false,
        t: 0
    },
    quartic: {
        points: [
            { x: 80, y: 320 },
            { x: 180, y: 80 },
            { x: 300, y: 320 },
            { x: 420, y: 80 },
            { x: 520, y: 320 }
        ],
        animating: false,
        t: 0
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeTabs();
    initializeCanvases();
    initializeSpeedControls();
    drawAllCurves();
});

// 初始化标签切换
function initializeTabs() {
    const tabs = document.querySelectorAll('.tab-btn');
    const sections = document.querySelectorAll('.curve-section');

    tabs.forEach(tab => {
        tab.addEventListener('click', function () {
            const curveType = this.dataset.curve;

            // 更新标签状态
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 更新内容区域
            sections.forEach(s => s.classList.remove('active'));
            document.getElementById(`${curveType}-section`).classList.add('active');

            currentCurve = curveType;
            drawCurve(curveType);
        });
    });
}

// 初始化画布
function initializeCanvases() {
    const canvasIds = ['linear-canvas', 'quadratic-canvas', 'cubic-canvas', 'quartic-canvas'];

    canvasIds.forEach(canvasId => {
        const canvas = document.getElementById(canvasId);
        const curveType = canvasId.replace('-canvas', '');

        // 鼠标事件
        canvas.addEventListener('mousedown', (e) => handleMouseDown(e, curveType));
        canvas.addEventListener('mousemove', (e) => handleMouseMove(e, curveType));
        canvas.addEventListener('mouseup', handleMouseUp);
        canvas.addEventListener('mouseleave', handleMouseUp);

        // 触摸事件（移动端支持）
        canvas.addEventListener('touchstart', (e) => handleTouchStart(e, curveType));
        canvas.addEventListener('touchmove', (e) => handleTouchMove(e, curveType));
        canvas.addEventListener('touchend', handleTouchEnd);
    });
}

// 初始化速度控制
function initializeSpeedControls() {
    const speedControls = ['linear-speed', 'quadratic-speed', 'cubic-speed', 'quartic-speed'];

    speedControls.forEach(controlId => {
        const control = document.getElementById(controlId);
        const valueDisplay = document.getElementById(controlId + '-value');

        control.addEventListener('input', function () {
            valueDisplay.textContent = this.value + 'x';
        });
    });
}

// 鼠标按下事件
function handleMouseDown(e, curveType) {
    const canvas = e.target;
    const rect = canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) * (canvas.width / rect.width);
    const y = (e.clientY - rect.top) * (canvas.height / rect.height);

    const pointIndex = findNearestPoint(x, y, curveType);
    if (pointIndex !== -1) {
        isDragging = true;
        dragPointIndex = pointIndex;
        dragCurveType = curveType;
        canvas.style.cursor = 'grabbing';
    }
}

// 鼠标移动事件
function handleMouseMove(e, curveType) {
    const canvas = e.target;
    const rect = canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) * (canvas.width / rect.width);
    const y = (e.clientY - rect.top) * (canvas.height / rect.height);

    if (isDragging && dragCurveType === curveType) {
        // 更新控制点位置
        curveData[curveType].points[dragPointIndex] = { x, y };
        drawCurve(curveType);
    } else {
        // 检查是否悬停在控制点上
        const pointIndex = findNearestPoint(x, y, curveType);
        canvas.style.cursor = pointIndex !== -1 ? 'grab' : 'crosshair';
    }
}

// 鼠标释放事件
function handleMouseUp() {
    isDragging = false;
    dragPointIndex = -1;
    dragCurveType = '';

    // 恢复所有画布的光标
    const canvases = document.querySelectorAll('canvas');
    canvases.forEach(canvas => {
        canvas.style.cursor = 'crosshair';
    });
}

// 触摸事件处理
function handleTouchStart(e, curveType) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    handleMouseDown(mouseEvent, curveType);
}

function handleTouchMove(e, curveType) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    handleMouseMove(mouseEvent, curveType);
}

function handleTouchEnd(e) {
    e.preventDefault();
    handleMouseUp();
}

// 查找最近的控制点
function findNearestPoint(x, y, curveType) {
    const points = curveData[curveType].points;
    const threshold = 20; // 点击阈值

    for (let i = 0; i < points.length; i++) {
        const dx = x - points[i].x;
        const dy = y - points[i].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance <= threshold) {
            return i;
        }
    }

    return -1;
}

// 绘制所有曲线
function drawAllCurves() {
    Object.keys(curveData).forEach(curveType => {
        drawCurve(curveType);
    });
}

// 绘制指定类型的贝塞尔曲线
function drawCurve(curveType) {
    const canvas = document.getElementById(`${curveType}-canvas`);
    const ctx = canvas.getContext('2d');
    const data = curveData[curveType];

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置画布样式
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 绘制贝塞尔曲线
    drawBezierCurve(ctx, data.points, curveType);

    // 绘制控制点和控制线
    drawControlElements(ctx, data.points, curveType);

    // 如果正在动画，绘制动画元素
    if (data.animating) {
        drawAnimationElements(ctx, data.points, data.t, curveType);
    }
}

// 绘制贝塞尔曲线
function drawBezierCurve(ctx, points, curveType) {
    ctx.beginPath();
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;

    // 绘制平滑的贝塞尔曲线
    const steps = 100;
    for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const point = calculateBezierPoint(points, t);

        if (i === 0) {
            ctx.moveTo(point.x, point.y);
        } else {
            ctx.lineTo(point.x, point.y);
        }
    }

    ctx.stroke();
}

// 绘制控制元素（控制点和控制线）
function drawControlElements(ctx, points, curveType) {
    // 绘制控制线
    ctx.beginPath();
    ctx.strokeStyle = '#bdc3c7';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);

    for (let i = 0; i < points.length - 1; i++) {
        ctx.moveTo(points[i].x, points[i].y);
        ctx.lineTo(points[i + 1].x, points[i + 1].y);
    }

    ctx.stroke();
    ctx.setLineDash([]);

    // 绘制控制点
    points.forEach((point, index) => {
        ctx.beginPath();

        if (index === 0 || index === points.length - 1) {
            // 端点 - 绿色
            ctx.fillStyle = '#27ae60';
            ctx.strokeStyle = '#2ecc71';
        } else {
            // 控制点 - 红色
            ctx.fillStyle = '#e74c3c';
            ctx.strokeStyle = '#c0392b';
        }

        ctx.lineWidth = 2;
        ctx.arc(point.x, point.y, 8, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();

        // 绘制点的标签
        ctx.fillStyle = '#2c3e50';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`P${index}`, point.x, point.y - 15);
    });
}

// 绘制动画元素
function drawAnimationElements(ctx, points, t, curveType) {
    let showConstruction = null;

    if (curveType === 'quadratic') {
        showConstruction = document.getElementById('show-construction');
    } else if (curveType === 'cubic') {
        showConstruction = document.getElementById('show-cubic-construction');
    } else if (curveType === 'quartic') {
        showConstruction = document.getElementById('show-quartic-construction');
    }

    if (curveType !== 'linear' && showConstruction && showConstruction.checked) {
        drawConstructionLines(ctx, points, t, curveType);
    }

    // 绘制当前点
    const currentPoint = calculateBezierPoint(points, t);
    ctx.beginPath();
    ctx.fillStyle = '#f39c12';
    ctx.strokeStyle = '#e67e22';
    ctx.lineWidth = 3;
    ctx.arc(currentPoint.x, currentPoint.y, 10, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // 绘制轨迹
    drawTrail(ctx, points, t);
}

// 绘制构造线（德卡斯特里奥算法可视化）
function drawConstructionLines(ctx, points, t, curveType) {
    const levels = [];
    levels.push([...points]);

    // 计算各级中间点
    for (let level = 0; level < points.length - 1; level++) {
        const currentLevel = levels[level];
        const nextLevel = [];

        for (let i = 0; i < currentLevel.length - 1; i++) {
            const p1 = currentLevel[i];
            const p2 = currentLevel[i + 1];
            const interpolated = {
                x: (1 - t) * p1.x + t * p2.x,
                y: (1 - t) * p1.y + t * p2.y
            };
            nextLevel.push(interpolated);
        }

        levels.push(nextLevel);
    }

    // 绘制构造线和中间点
    for (let level = 1; level < levels.length; level++) {
        const currentLevel = levels[level];
        const alpha = 0.7 - (level - 1) * 0.2;

        // 绘制连接线
        if (currentLevel.length > 1) {
            ctx.beginPath();
            ctx.strokeStyle = `rgba(52, 152, 219, ${alpha})`;
            ctx.lineWidth = 2;

            for (let i = 0; i < currentLevel.length - 1; i++) {
                ctx.moveTo(currentLevel[i].x, currentLevel[i].y);
                ctx.lineTo(currentLevel[i + 1].x, currentLevel[i + 1].y);
            }

            ctx.stroke();
        }

        // 绘制中间点
        currentLevel.forEach(point => {
            ctx.beginPath();
            ctx.fillStyle = `rgba(52, 152, 219, ${alpha})`;
            ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
            ctx.fill();
        });
    }
}

// 绘制轨迹
function drawTrail(ctx, points, currentT) {
    ctx.beginPath();
    ctx.strokeStyle = 'rgba(243, 156, 18, 0.6)';
    ctx.lineWidth = 2;

    const steps = Math.floor(currentT * 50);
    for (let i = 0; i <= steps; i++) {
        const t = i / 50;
        const point = calculateBezierPoint(points, t);

        if (i === 0) {
            ctx.moveTo(point.x, point.y);
        } else {
            ctx.lineTo(point.x, point.y);
        }
    }

    ctx.stroke();
}

// 计算贝塞尔曲线上的点
function calculateBezierPoint(points, t) {
    if (points.length === 2) {
        // 线性贝塞尔曲线
        return {
            x: (1 - t) * points[0].x + t * points[1].x,
            y: (1 - t) * points[0].y + t * points[1].y
        };
    } else if (points.length === 3) {
        // 二次贝塞尔曲线
        const u = 1 - t;
        return {
            x: u * u * points[0].x + 2 * u * t * points[1].x + t * t * points[2].x,
            y: u * u * points[0].y + 2 * u * t * points[1].y + t * t * points[2].y
        };
    } else if (points.length === 4) {
        // 三次贝塞尔曲线
        const u = 1 - t;
        const u2 = u * u;
        const u3 = u2 * u;
        const t2 = t * t;
        const t3 = t2 * t;

        return {
            x: u3 * points[0].x + 3 * u2 * t * points[1].x + 3 * u * t2 * points[2].x + t3 * points[3].x,
            y: u3 * points[0].y + 3 * u2 * t * points[1].y + 3 * u * t2 * points[2].y + t3 * points[3].y
        };
    } else if (points.length === 5) {
        // 四次贝塞尔曲线
        const u = 1 - t;
        const u2 = u * u;
        const u3 = u2 * u;
        const u4 = u3 * u;
        const t2 = t * t;
        const t3 = t2 * t;
        const t4 = t3 * t;

        return {
            x: u4 * points[0].x + 4 * u3 * t * points[1].x + 6 * u2 * t2 * points[2].x + 4 * u * t3 * points[3].x + t4 * points[4].x,
            y: u4 * points[0].y + 4 * u3 * t * points[1].y + 6 * u2 * t2 * points[2].y + 4 * u * t3 * points[3].y + t4 * points[4].y
        };
    }

    // 通用德卡斯特里奥算法（用于任意次数）
    return deCasteljau(points, t);
}

// 德卡斯特里奥算法
function deCasteljau(points, t) {
    if (points.length === 1) {
        return points[0];
    }

    const newPoints = [];
    for (let i = 0; i < points.length - 1; i++) {
        newPoints.push({
            x: (1 - t) * points[i].x + t * points[i + 1].x,
            y: (1 - t) * points[i].y + t * points[i + 1].y
        });
    }

    return deCasteljau(newPoints, t);
}

// 动画函数
function animateLinear() {
    startAnimation('linear');
}

function animateQuadratic() {
    startAnimation('quadratic');
}

function animateCubic() {
    startAnimation('cubic');
}

function animateQuartic() {
    startAnimation('quartic');
}

function startAnimation(curveType) {
    const data = curveData[curveType];

    if (data.animating) {
        // 停止动画
        data.animating = false;
        data.t = 0;
        if (animationFrames[curveType]) {
            cancelAnimationFrame(animationFrames[curveType]);
        }
        drawCurve(curveType);
        return;
    }

    // 开始动画
    data.animating = true;
    data.t = 0;

    const speed = parseFloat(document.getElementById(`${curveType}-speed`).value);

    function animate() {
        if (!data.animating) return;

        data.t += 0.01 * speed;

        if (data.t >= 1) {
            data.t = 1;
            data.animating = false;
        }

        drawCurve(curveType);

        if (data.animating) {
            animationFrames[curveType] = requestAnimationFrame(animate);
        }
    }

    animate();
}

// 重置函数
function resetLinear() {
    resetCurve('linear');
}

function resetQuadratic() {
    resetCurve('quadratic');
}

function resetCubic() {
    resetCurve('cubic');
}

function resetQuartic() {
    resetCurve('quartic');
}

function resetCurve(curveType) {
    const data = curveData[curveType];

    // 停止动画
    data.animating = false;
    data.t = 0;
    if (animationFrames[curveType]) {
        cancelAnimationFrame(animationFrames[curveType]);
    }

    // 重置控制点位置
    if (curveType === 'linear') {
        data.points = [
            { x: 100, y: 200 },
            { x: 500, y: 200 }
        ];
    } else if (curveType === 'quadratic') {
        data.points = [
            { x: 100, y: 300 },
            { x: 300, y: 100 },
            { x: 500, y: 300 }
        ];
    } else if (curveType === 'cubic') {
        data.points = [
            { x: 100, y: 300 },
            { x: 200, y: 100 },
            { x: 400, y: 100 },
            { x: 500, y: 300 }
        ];
    } else if (curveType === 'quartic') {
        data.points = [
            { x: 80, y: 320 },
            { x: 180, y: 80 },
            { x: 300, y: 320 },
            { x: 420, y: 80 },
            { x: 520, y: 320 }
        ];
    }

    drawCurve(curveType);
}
