<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM机制——Transformer如何处理一句话？</title>
    <link rel="stylesheet" href="index6.css">
    <link rel="stylesheet" href="Deep.css">
</head>

<body>
    <!-- 进度指示器 -->
    <div class="progress-indicator">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <!-- 浮动帮助按钮 -->
    <div class="floating-help" onclick="toggleHelp()">
        <span style="font-size: 1.5em;">?</span>
        <div class="help-tooltip" id="helpTooltip">
            <strong>🎯 使用指南：</strong><br>
            <strong>📚 学习建议：</strong><br>
            • 按顺序体验6个模块，理解每步的目的<br>
            • 注意每个模块中的"为什么需要"和"生活类比"<br>
            • 尝试输入不同的句子观察变化<br>
            • 思考各步骤之间的连接关系<br><br>
            <strong>🔍 操作提示：</strong><br>
            • 点击模块按钮打开演示<br>
            • 使用随机示例按钮尝试不同文本<br>
            • 按ESC键关闭演示窗口<br>
            • 滚动页面查看进度条
        </div>
    </div>

    <div class="container">
        <!-- 新增：神经网络与深度学习模块 -->
        <div class="neural-network-section">
            <div class="header">
                <h1>🧠 LLM 原理根基——神经网络与深度学习</h1>
                <p>理解LLM的基础：从神经元到深度学习的演进之路</p>

                <div class="intro-text">
                    <p>LLM 是一种基于深度学习技术的自然语言处理模型，而在谈深度学习之前，必须先介绍神经网络。LLM
                        是基于拥有大量参数的神经网络组成，神经网络模型透过3大层来使人工神经元互连：输入层、隐藏层和输出层，凭借其多层结构让LLM 模型能有效地处理复杂的语言任务。</p>

                    <p>深度学习建立在神经网络的基础上，深度学习进一步扩展其能力，深度学习模型通过堆叠多层神经网络，构建出深度的神经网络模型，每一层神经网络都学习到不同的数据特征。也因此LLM
                        能够分辨出更复杂、更准确的资讯。</p>
                </div>
            </div>

            <!-- 神经网络基础演示 -->
            <div class="neural-demo-container">
                <button class="main-demo-btn" onclick="openNeuralDemo()">
                    🧠 开始神经网络交互式学习
                </button>
            </div>
        </div>

        <!-- 分隔线 -->
        <div class="section-divider">
            <div class="divider-line"></div>
            <div class="divider-text">从基础到应用</div>
            <div class="divider-line"></div>
        </div>

        <div class="header">
            <h1>🤖 LLM机制——Transformer如何处理一句话？</h1>
            <p>通过交互式演示深入理解Transformer的工作原理</p>

            <!-- 添加整体流程图 -->
            <div
                style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 20px; margin: 20px 0; backdrop-filter: blur(10px);">
                <h3 style="color: white; margin-bottom: 15px; text-align: center;">🔄 Transformer处理流程总览</h3>
                <div style="display: flex; align-items: center; justify-content: center; flex-wrap: wrap; gap: 10px;">
                    <div class="flow-step">文本输入</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step">词嵌入</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step">位置编码</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step">注意力机制</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step">前馈网络</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step">归一化</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step">预测输出</div>
                </div>
                <p style="color: rgba(255,255,255,0.8); text-align: center; margin-top: 15px; font-size: 0.9em;">
                    💡 每个步骤都有其独特作用，共同协作让AI理解和生成人类语言
                </p>
            </div>
        </div>

        <div class="modules-grid">
            <!-- 模块1: 输入嵌入 -->
            <div class="module fade-in">
                <h3><span class="module-number">1</span>输入嵌入 (Input Embedding)</h3>
                <p>将输入的文本转换为数字向量表示。每个词汇都会被映射到一个高维向量空间中，这些向量包含了词汇的语义信息。</p>

                <button class="collapsible" onclick="toggleCollapsible(this)">
                    <span>📖 展开查看详细解析</span>
                    <span class="chevron">▼</span>
                </button>
                <div class="collapsible-content">
                    <div class="purpose-section">
                        <div class="purpose-title">🎯 为什么需要这一步？</div>
                        <p>计算机只能理解数字，不能直接处理文字。就像翻译官需要把不同语言转换成通用语言一样，我们需要把文字转换成数字向量，让AI能够"理解"文字的含义。</p>
                    </div>

                    <div class="analogy-section">
                        <div class="analogy-title">🏠 生活类比</div>
                        <p>想象每个词都有一个"身份证"，上面记录了这个词的各种特征（意思、用法、情感等）。词嵌入就是给每个词制作这样一张数字化的"身份证"。</p>
                    </div>

                    <div class="connection-section">
                        <div class="connection-title">🔗 与下一步的关系</div>
                        <p>词嵌入为每个词提供了基础的语义信息，但还缺少位置信息。下一步的位置编码会告诉AI这些词在句子中的位置关系。</p>
                    </div>

                    <div class="code-section">
                        <button class="code-btn" onclick="showCoreCode('embedding')">
                            💻 实现该效果的核心代码
                        </button>
                    </div>
                </div>

                <button class="demo-btn" onclick="openDemo('embedding')">🔍 查看嵌入演示</button>
            </div>

            <!-- 模块2: 位置编码 -->
            <div class="module fade-in">
                <h3><span class="module-number">2</span>位置编码 (Positional Encoding)</h3>
                <p>为每个位置的词汇添加位置信息，让模型能够理解词汇在句子中的相对位置关系，这对于理解语言的顺序性至关重要。</p>

                <button class="collapsible" onclick="toggleCollapsible(this)">
                    <span>📖 展开查看详细解析</span>
                    <span class="chevron">▼</span>
                </button>
                <div class="collapsible-content">
                    <div class="purpose-section">
                        <div class="purpose-title">🎯 为什么需要这一步？</div>
                        <p>语言是有顺序的！"我喜欢你"和"你喜欢我"意思完全不同。但词嵌入只告诉AI每个词的意思，不知道词的位置。位置编码就是给每个词加上"座位号"。</p>
                    </div>

                    <div class="analogy-section">
                        <div class="analogy-title">🎭 生活类比</div>
                        <p>就像看电影时，演员的台词内容很重要，但说话的先后顺序同样重要。位置编码就像给每句台词标上时间戳，让AI知道哪句话先说，哪句话后说。</p>
                    </div>

                    <div class="connection-section">
                        <div class="connection-title">🔗 与下一步的关系</div>
                        <p>现在每个词既有了语义信息（词嵌入）又有了位置信息（位置编码）。下一步的注意力机制将利用这些信息，让AI学会关注句子中最重要的词汇关系。</p>
                    </div>

                    <div class="code-section">
                        <button class="code-btn" onclick="showCoreCode('positional')">
                            💻 实现该效果的核心代码
                        </button>
                    </div>
                </div>

                <button class="demo-btn" onclick="openDemo('positional')">📍 查看位置编码演示</button>
            </div>

            <!-- 模块3: 多头注意力机制 -->
            <div class="module fade-in">
                <h3><span class="module-number">3</span>多头注意力 (Multi-Head Attention)</h3>
                <p>计算每个词与句子中其他词的关联程度，通过多个"注意力头"从不同角度理解词汇间的关系，捕获复杂的语义依赖。</p>

                <button class="collapsible" onclick="toggleCollapsible(this)">
                    <span>📖 展开查看详细解析</span>
                    <span class="chevron">▼</span>
                </button>
                <div class="collapsible-content">
                    <div class="purpose-section">
                        <div class="purpose-title">🎯 为什么需要这一步？</div>
                        <p>这是Transformer的核心！人类理解语言时会自动关注重要的词汇关系。比如"小明的妈妈很漂亮"中，"妈妈"和"小明"有关系，"漂亮"描述"妈妈"。注意力机制让AI学会这种关联能力。
                        </p>
                    </div>

                    <div class="analogy-section">
                        <div class="analogy-title">👀 生活类比</div>
                        <p>就像在嘈杂的餐厅里，你能自动"注意"到朋友的声音而忽略其他噪音。多头注意力就是给AI多双"耳朵"，从不同角度同时关注词汇间的各种关系（语法关系、语义关系等）。</p>
                    </div>

                    <div class="connection-section">
                        <div class="connection-title">🔗 与下一步的关系</div>
                        <p>注意力机制找出了词汇间的重要关系，但这些信息还需要进一步处理和抽象。下一步的前馈网络会对这些关系信息进行深度加工，提取更高层次的特征。</p>
                    </div>

                    <div class="code-section">
                        <button class="code-btn" onclick="showCoreCode('attention')">
                            💻 实现该效果的核心代码
                        </button>
                    </div>
                </div>

                <button class="demo-btn" onclick="openDemo('attention')">🎯 查看注意力演示</button>
            </div>

            <!-- 模块4: 前馈神经网络 -->
            <div class="module fade-in">
                <h3><span class="module-number">4</span>前馈网络 (Feed Forward)</h3>
                <p>对每个位置的表示进行非线性变换，增强模型的表达能力。通过两层全连接网络对特征进行进一步处理和抽象。</p>

                <button class="collapsible" onclick="toggleCollapsible(this)">
                    <span>📖 展开查看详细解析</span>
                    <span class="chevron">▼</span>
                </button>
                <div class="collapsible-content">
                    <div class="purpose-section">
                        <div class="purpose-title">🎯 为什么需要这一步？</div>
                        <p>注意力机制找到了词汇关系，但这些信息还比较"原始"。前馈网络就像一个"思考器"，对这些关系进行深度分析和抽象，提取出更高层次的语义特征。</p>
                    </div>

                    <div class="analogy-section">
                        <div class="analogy-title">🧠 生活类比</div>
                        <p>就像大脑的不同区域：注意力机制像"感知区域"收集信息，前馈网络像"思考区域"分析信息。它把简单的词汇关系加工成复杂的语义理解。</p>
                    </div>

                    <div class="connection-section">
                        <div class="connection-title">🔗 与下一步的关系</div>
                        <p>前馈网络处理后的信息更加丰富和抽象，但训练深层网络容易出现问题。下一步的归一化和残差连接会稳定这个过程，确保信息能够顺利传递。</p>
                    </div>

                    <div class="code-section">
                        <button class="code-btn" onclick="showCoreCode('feedforward')">
                            💻 实现该效果的核心代码
                        </button>
                    </div>
                </div>

                <button class="demo-btn" onclick="openDemo('feedforward')">⚡ 查看前馈网络演示</button>
            </div>

            <!-- 模块5: 层归一化与残差连接 -->
            <div class="module fade-in">
                <h3><span class="module-number">5</span>归一化连接 (Layer Norm & Residual)</h3>
                <p>通过层归一化稳定训练过程，残差连接帮助梯度传播，防止深层网络中的梯度消失问题，提高模型训练效果。</p>

                <button class="collapsible" onclick="toggleCollapsible(this)">
                    <span>📖 展开查看详细解析</span>
                    <span class="chevron">▼</span>
                </button>
                <div class="collapsible-content">
                    <div class="purpose-section">
                        <div class="purpose-title">🎯 为什么需要这一步？</div>
                        <p>深层神经网络训练很困难，容易出现"梯度消失"（信息传递不畅）。归一化让数据保持稳定范围，残差连接建立"信息高速公路"，确保原始信息不会丢失。</p>
                    </div>

                    <div class="analogy-section">
                        <div class="analogy-title">🛣️ 生活类比</div>
                        <p>想象信息传递像接力赛：归一化确保每个选手都以相同的体力状态接棒，残差连接像在跑道旁建了一条直达终点的高速路，即使中间有选手掉棒，信息也能直接到达。</p>
                    </div>

                    <div class="connection-section">
                        <div class="connection-title">🔗 与下一步的关系</div>
                        <p>经过前面所有步骤的处理，现在每个词都包含了丰富的语义和关系信息。最后一步将把这些信息转换成具体的预测结果，告诉我们下一个最可能出现的词是什么。</p>
                    </div>

                    <div class="code-section">
                        <button class="code-btn" onclick="showCoreCode('normalization')">
                            💻 实现该效果的核心代码
                        </button>
                    </div>
                </div>

                <button class="demo-btn" onclick="openDemo('normalization')">🔄 查看归一化演示</button>
            </div>

            <!-- 模块6: 输出预测 -->
            <div class="module fade-in">
                <h3><span class="module-number">6</span>输出预测 (Output Prediction)</h3>
                <p>将最终的隐藏状态转换为词汇表上的概率分布，预测下一个最可能的词汇。通过softmax函数得到每个词的概率。</p>

                <button class="collapsible" onclick="toggleCollapsible(this)">
                    <span>📖 展开查看详细解析</span>
                    <span class="chevron">▼</span>
                </button>
                <div class="collapsible-content">
                    <div class="purpose-section">
                        <div class="purpose-title">🎯 为什么需要这一步？</div>
                        <p>前面所有步骤都是为了这一刻！AI已经充分理解了输入的句子，现在需要给出具体的预测结果。这一步将抽象的理解转换成具体的词汇预测。</p>
                    </div>

                    <div class="analogy-section">
                        <div class="analogy-title">🎯 生活类比</div>
                        <p>就像考试时，你读完题目、分析问题、思考答案后，最终要在答题卡上写下具体答案。输出预测就是AI的"答题"过程，把内部的理解转换成外部可见的结果。</p>
                    </div>

                    <div class="connection-section">
                        <div class="connection-title">🔗 整个流程的总结</div>
                        <p>🔄
                            <strong>完整循环：</strong>文本→数字化→位置感知→关系理解→深度思考→稳定传递→预测输出。每个环节都不可缺少，共同构成了Transformer强大的语言理解和生成能力！
                        </p>
                    </div>

                    <div class="code-section">
                        <button class="code-btn" onclick="showCoreCode('output')">
                            💻 实现该效果的核心代码
                        </button>
                    </div>
                </div>

                <button class="demo-btn" onclick="openDemo('output')">📊 查看输出预测演示</button>
            </div>
        </div>

        <!-- 添加总结部分 -->
        <div
            style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 30px; margin: 40px 0; backdrop-filter: blur(10px);">
            <h2 style="color: white; text-align: center; margin-bottom: 25px;">🧠 Transformer工作原理总结</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: #FFD700; margin-bottom: 10px;">🔄 核心思想</h4>
                    <p style="color: rgba(255,255,255,0.9); line-height: 1.6;">
                        Transformer通过"注意力机制"让AI学会像人类一样理解语言中的关键关系，
                        每个步骤都有明确的目的，共同构建强大的语言理解能力。
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: #FFD700; margin-bottom: 10px;">🎯 关键创新</h4>
                    <p style="color: rgba(255,255,255,0.9); line-height: 1.6;">
                        不同于传统方法逐个处理词汇，Transformer能同时关注整个句子中的所有词汇关系，
                        大大提高了理解效率和准确性。
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: #FFD700; margin-bottom: 10px;">🚀 实际应用</h4>
                    <p style="color: rgba(255,255,255,0.9); line-height: 1.6;">
                        ChatGPT、GPT-4等现代AI都基于Transformer架构，
                        它们能够进行对话、写作、翻译、编程等复杂任务。
                    </p>
                </div>
            </div>

            <div style="text-align: center; margin-top: 25px;">
                <p style="color: rgba(255,255,255,0.8); font-size: 1.1em;">
                    💡 <strong>记住：</strong>每个环节都不可缺少，它们协同工作才能实现AI的"智能"！
                </p>
            </div>
        </div>
    </div>

    <!-- Transformer完整流程演示 -->
    <div class="transformer-flow-section">
        <div class="flow-header">
            <h2>🚀 Transformer完整处理流程演示</h2>
            <p>输入一句话，观察Transformer如何一步步处理并生成输出</p>
        </div>

        <div class="flow-input-area">
            <div class="input-controls">
                <input type="text" id="flowInput" class="flow-input" placeholder="请输入一句话，例如：我喜欢学习人工智能"
                    value="我喜欢学习人工智能">
                <button class="flow-btn primary" onclick="startTransformerFlow()">🎬 开始处理</button>
                <button class="flow-btn secondary" onclick="resetTransformerFlow()">🔄 重置</button>
            </div>
        </div>

        <div class="flow-steps-container" id="flowStepsContainer">
            <div class="flow-step" id="step1" data-step="1">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <h3>🔍 输入嵌入</h3>
                    <div class="step-status" id="status1">等待开始</div>
                </div>
                <div class="step-content" id="content1">
                    <p>将文字转换为数字向量...</p>
                </div>
            </div>

            <div class="flow-step" id="step2" data-step="2">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <h3>📍 位置编码</h3>
                    <div class="step-status" id="status2">等待开始</div>
                </div>
                <div class="step-content" id="content2">
                    <p>添加位置信息到词向量...</p>
                </div>
            </div>

            <div class="flow-step" id="step3" data-step="3">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <h3>🎯 多头注意力</h3>
                    <div class="step-status" id="status3">等待开始</div>
                </div>
                <div class="step-content" id="content3">
                    <p>计算词汇间的关注度...</p>
                </div>
            </div>

            <div class="flow-step" id="step4" data-step="4">
                <div class="step-header">
                    <div class="step-number">4</div>
                    <h3>⚡ 前馈网络</h3>
                    <div class="step-status" id="status4">等待开始</div>
                </div>
                <div class="step-content" id="content4">
                    <p>通过神经网络处理...</p>
                </div>
            </div>

            <div class="flow-step" id="step5" data-step="5">
                <div class="step-header">
                    <div class="step-number">5</div>
                    <h3>🔄 归一化与残差</h3>
                    <div class="step-status" id="status5">等待开始</div>
                </div>
                <div class="step-content" id="content5">
                    <p>标准化数据并添加残差连接...</p>
                </div>
            </div>

            <div class="flow-step" id="step6" data-step="6">
                <div class="step-header">
                    <div class="step-number">6</div>
                    <h3>📊 输出预测</h3>
                    <div class="step-status" id="status6">等待开始</div>
                </div>
                <div class="step-content" id="content6">
                    <p>生成最终预测结果...</p>
                </div>
            </div>
        </div>

        <div class="flow-result" id="flowResult">
            <h3>🎯 处理结果</h3>
            <div class="result-content" id="resultContent">
                点击"开始处理"查看Transformer如何处理您的输入
            </div>
        </div>
    </div>

    <!-- 演示模态框 -->
    <div id="demoModal" class="demo-modal">
        <div class="modal-content">
            <span class="close" onclick="closeDemo()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <!-- 神经网络演示模态框 -->
    <div id="neuralModal" class="neural-modal">
        <div class="neural-modal-content">
            <span class="close" onclick="closeNeuralDemo()">&times;</span>
            <div id="neuralModalContent">
                <!-- 神经网络演示内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 核心代码弹窗 -->
    <div id="codeModal" class="code-modal">
        <div class="code-modal-content">
            <div class="code-modal-header">
                <h3 id="codeModalTitle">核心代码</h3>
                <span class="code-modal-close" onclick="closeCoreCodeModal()">&times;</span>
            </div>
            <div class="code-modal-body">
                <div id="codeModalDescription" class="code-description"></div>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-language">JavaScript</span>
                        <button class="copy-code-btn" onclick="copyCodeToClipboard()">📋 复制代码</button>
                    </div>
                    <pre id="codeModalContent" class="code-content"></pre>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="CoreCodes.js"></script>
    <script type="text/javascript" src="NeuralNetworkCodes.js"></script>
    <script type="text/javascript" src="Deep.js"></script>
    <script type="text/javascript" src="index6.js"></script>
</body>

</html>