<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP模型上下文协议 - 三层架构交互演示</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="container">
        <!-- 标题和说明 -->
        <header class="header">
            <h1>🔌 MCP模型上下文协议演示</h1>
            <p class="subtitle">通过"智能助手请求处理"体验Host-Client-Server三层架构的通信机制</p>
        </header>

        <!-- 核心概念说明 -->
        <section class="concept-section">
            <div class="concept-card">
                <h3>💡 什么是MCP？</h3>
                <p><strong>模型上下文协议（Model Context Protocol,
                        MCP）</strong>是一种连接大型语言模型与外部世界的开放式通信协议，通过<strong>客户端-服务器架构</strong>实现AI应用的"即插即用"。</p>
                <div class="analogy">
                    <span class="analogy-icon">🔌</span>
                    <p><strong>核心理念：</strong>就像USB-C统一了电子设备插口，MCP希望成为AI应用中标准的上下文接口。</p>
                </div>
            </div>
        </section>

        <!-- MCP三层架构展示 -->
        <section class="architecture-section">
            <h3>🏗️ MCP三层架构实时演示</h3>
            <div class="architecture-container">
                <!-- Host层 -->
                <div class="layer host-layer" id="hostLayer">
                    <div class="layer-header">
                        <span class="layer-icon">📱</span>
                        <h4>Host层 - 用户控制起点</h4>
                    </div>
                    <div class="layer-content">
                        <div class="user-interface">
                            <h5>🤖 智能助手界面</h5>
                            <div class="request-form">
                                <label>选择请求类型：</label>
                                <select id="requestType">
                                    <option value="">-- 请选择请求类型 --</option>
                                    <option value="weather">天气查询</option>
                                    <option value="translate">文本翻译</option>
                                    <option value="calculate">数学计算</option>
                                    <option value="search">信息搜索</option>
                                </select>

                                <label>输入请求内容：</label>
                                <textarea id="requestContent" placeholder="请输入您的请求内容..." rows="3"></textarea>

                                <button id="sendRequest" disabled>🚀 发送请求</button>
                            </div>
                            <div class="response-area" id="responseArea">
                                <h6>响应结果：</h6>
                                <div class="response-content" id="responseContent">
                                    <p>等待用户发送请求...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layer-status" id="hostStatus">待机中</div>
                </div>

                <!-- 通信箭头 Host -> Client -->
                <div class="communication-flow">
                    <div class="arrow-container" id="hostToClient">
                        <div class="arrow-line"></div>
                        <div class="arrow-head">→</div>
                        <div class="message-bubble" id="hostMessage"></div>
                    </div>
                </div>

                <!-- Client层 -->
                <div class="layer client-layer" id="clientLayer">
                    <div class="layer-header">
                        <span class="layer-icon">🔄</span>
                        <h4>Client层 - 协议守门人</h4>
                    </div>
                    <div class="layer-content">
                        <div class="client-functions">
                            <div class="function-item">
                                <span class="function-icon">🔐</span>
                                <div class="function-info">
                                    <h6>安全验证</h6>
                                    <div class="status-indicator" id="authStatus">未验证</div>
                                </div>
                            </div>
                            <div class="function-item">
                                <span class="function-icon">🔄</span>
                                <div class="function-info">
                                    <h6>协议转换</h6>
                                    <div class="status-indicator" id="protocolStatus">待转换</div>
                                </div>
                            </div>
                            <div class="function-item">
                                <span class="function-icon">📡</span>
                                <div class="function-info">
                                    <h6>请求路由</h6>
                                    <div class="status-indicator" id="routingStatus">待路由</div>
                                </div>
                            </div>
                        </div>
                        <div class="processing-log">
                            <h6>处理日志：</h6>
                            <div class="log-content" id="clientLog">
                                <div class="log-entry">Client层就绪，等待请求...</div>
                            </div>
                        </div>
                    </div>
                    <div class="layer-status" id="clientStatus">就绪</div>
                </div>

                <!-- 通信箭头 Client -> Server -->
                <div class="communication-flow">
                    <div class="arrow-container" id="clientToServer">
                        <div class="arrow-line"></div>
                        <div class="arrow-head">→</div>
                        <div class="message-bubble" id="clientMessage"></div>
                    </div>
                </div>

                <!-- Server层 -->
                <div class="layer server-layer" id="serverLayer">
                    <div class="layer-header">
                        <span class="layer-icon">🏪</span>
                        <h4>Server层 - 服务调度中心</h4>
                    </div>
                    <div class="layer-content">
                        <div class="server-resources">
                            <div class="resource-item" data-service="weather">
                                <span class="resource-icon">🌤️</span>
                                <div class="resource-info">
                                    <h6>天气服务</h6>
                                    <div class="resource-status">可用</div>
                                </div>
                            </div>
                            <div class="resource-item" data-service="translate">
                                <span class="resource-icon">🌐</span>
                                <div class="resource-info">
                                    <h6>翻译服务</h6>
                                    <div class="resource-status">可用</div>
                                </div>
                            </div>
                            <div class="resource-item" data-service="calculate">
                                <span class="resource-icon">🧮</span>
                                <div class="resource-info">
                                    <h6>计算服务</h6>
                                    <div class="resource-status">可用</div>
                                </div>
                            </div>
                            <div class="resource-item" data-service="search">
                                <span class="resource-icon">🔍</span>
                                <div class="resource-info">
                                    <h6>搜索服务</h6>
                                    <div class="resource-status">可用</div>
                                </div>
                            </div>
                        </div>
                        <div class="server-log">
                            <h6>服务日志：</h6>
                            <div class="log-content" id="serverLog">
                                <div class="log-entry">Server层启动，所有服务就绪...</div>
                            </div>
                        </div>
                    </div>
                    <div class="layer-status" id="serverStatus">运行中</div>
                </div>
            </div>

            <!-- 返回通信流 -->
            <div class="return-flow">
                <div class="return-arrow" id="serverToClient">
                    <div class="arrow-line"></div>
                    <div class="arrow-head">←</div>
                    <div class="message-bubble" id="serverMessage"></div>
                </div>
                <div class="return-arrow" id="clientToHost">
                    <div class="arrow-line"></div>
                    <div class="arrow-head">←</div>
                    <div class="message-bubble" id="returnMessage"></div>
                </div>
            </div>
        </section>

        <!-- 通信流程步骤展示 -->
        <section class="flow-section">
            <h3>📡 MCP通信流程步骤</h3>
            <div class="flow-steps">
                <div class="flow-step" id="step1">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h5>Host发起请求</h5>
                        <p>用户在界面输入请求，Host层接收并准备发送</p>
                    </div>
                </div>
                <div class="flow-step" id="step2">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h5>Client处理请求</h5>
                        <p>验证身份、转换协议、路由到合适的服务</p>
                    </div>
                </div>
                <div class="flow-step" id="step3">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h5>Server执行任务</h5>
                        <p>调用相应服务，处理业务逻辑，生成结果</p>
                    </div>
                </div>
                <div class="flow-step" id="step4">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h5>响应返回用户</h5>
                        <p>Server→Client→Host，最终展示给用户</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统监控面板 -->
        <section class="monitoring-section">
            <h3>📊 系统实时监控</h3>
            <div class="monitoring-grid">
                <div class="monitor-card">
                    <div class="monitor-icon">📈</div>
                    <div class="monitor-info">
                        <h5>请求总数</h5>
                        <div class="monitor-value" id="totalRequests">0</div>
                    </div>
                </div>
                <div class="monitor-card">
                    <div class="monitor-icon">⚡</div>
                    <div class="monitor-info">
                        <h5>平均响应时间</h5>
                        <div class="monitor-value" id="avgResponseTime">0ms</div>
                    </div>
                </div>
                <div class="monitor-card">
                    <div class="monitor-icon">✅</div>
                    <div class="monitor-info">
                        <h5>成功率</h5>
                        <div class="monitor-value" id="successRate">100%</div>
                    </div>
                </div>
                <div class="monitor-card">
                    <div class="monitor-icon">🔄</div>
                    <div class="monitor-info">
                        <h5>活跃连接</h5>
                        <div class="monitor-value" id="activeConnections">3</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- MCP知识点总结 -->
        <section class="knowledge-section">
            <h3>📚 MCP核心知识点</h3>
            <div class="knowledge-grid">
                <div class="knowledge-card">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">📱</span>
                        <h4>Host层 - 用户控制起点</h4>
                    </div>
                    <div class="knowledge-content">
                        <p><strong>主要职责：</strong></p>
                        <ul>
                            <li>接收用户的操作和输入</li>
                            <li>将请求发送给Client层</li>
                            <li>显示最终处理结果给用户</li>
                            <li>提供友好的用户交互界面</li>
                        </ul>
                        <p><strong>实际应用：</strong>ChatGPT界面、Claude界面、各种AI应用的前端</p>
                    </div>
                </div>

                <div class="knowledge-card">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔄</span>
                        <h4>Client层 - 协议守门人</h4>
                    </div>
                    <div class="knowledge-content">
                        <p><strong>主要职责：</strong></p>
                        <ul>
                            <li>身份验证和权限检查</li>
                            <li>协议转换（HTTP→JSON-RPC）</li>
                            <li>请求路由和负载均衡</li>
                            <li>错误处理和重试机制</li>
                        </ul>
                        <p><strong>实际应用：</strong>API网关、中间件、代理服务器</p>
                    </div>
                </div>

                <div class="knowledge-card">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🏪</span>
                        <h4>Server层 - 服务调度中心</h4>
                    </div>
                    <div class="knowledge-content">
                        <p><strong>主要职责：</strong></p>
                        <ul>
                            <li>管理各种工具和资源</li>
                            <li>执行具体的业务逻辑</li>
                            <li>调用外部API和服务</li>
                            <li>数据处理和结果生成</li>
                        </ul>
                        <p><strong>实际应用：</strong>文件系统、数据库、第三方API、计算服务</p>
                    </div>
                </div>

                <div class="knowledge-card">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">📡</span>
                        <h4>JSON-RPC通信协议</h4>
                    </div>
                    <div class="knowledge-content">
                        <p><strong>协议特点：</strong></p>
                        <ul>
                            <li>基于JSON的轻量级RPC协议</li>
                            <li>支持同步和异步调用</li>
                            <li>标准化的错误处理机制</li>
                            <li>跨语言、跨平台兼容</li>
                        </ul>
                        <p><strong>消息格式：</strong>包含method、params、id等字段</p>
                    </div>
                </div>

                <div class="knowledge-card">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔌</span>
                        <h4>即插即用特性</h4>
                    </div>
                    <div class="knowledge-content">
                        <p><strong>核心优势：</strong></p>
                        <ul>
                            <li>统一的接口标准</li>
                            <li>降低集成复杂度</li>
                            <li>提高开发效率</li>
                            <li>支持动态扩展</li>
                        </ul>
                        <p><strong>类比：</strong>就像USB接口统一了设备连接标准</p>
                    </div>
                </div>

                <div class="knowledge-card">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🛡️</span>
                        <h4>安全与隔离机制</h4>
                    </div>
                    <div class="knowledge-content">
                        <p><strong>安全保障：</strong></p>
                        <ul>
                            <li>多层身份验证</li>
                            <li>权限控制和访问限制</li>
                            <li>数据加密传输</li>
                            <li>服务间隔离</li>
                        </ul>
                        <p><strong>容错机制：</strong>优雅降级、自动重试、故障恢复</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 核心代码按钮 -->
        <div class="code-section">
            <button class="code-btn" onclick="showCoreCode()">
                🔍 实现该效果的核心JS代码
            </button>
        </div>
    </div>

    <!-- 代码展示模态框 -->
    <div class="modal" id="codeModal">
        <div class="modal-content">
            <span class="close" onclick="closeCoreCode()">&times;</span>
            <h3>MCP三层架构核心实现代码</h3>
            <pre id="codeDisplay"></pre>
        </div>
    </div>

    <script src="main.js"></script>
    <script src="code.js"></script>
</body>

</html>