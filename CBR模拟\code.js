// 核心代码展示 - CBR基于案例推理实现

function getCoreCode() {
    return `
// ===== CBR基于案例推理系统核心实现代码 =====

// 1. CBR系统主类
class CBRSystem {
    constructor() {
        this.caseLibrary = new Map();      // 案例库
        this.similarityEngine = new SimilarityEngine();  // 相似度计算引擎
        this.adaptationEngine = new AdaptationEngine();  // 案例适应引擎
        this.learningEngine = new LearningEngine();      // 学习引擎
    }

    // CBR四步核心流程
    async performCBR(newProblem) {
        try {
            // 步骤1: 检索 (Retrieve)
            const similarCases = await this.retrieve(newProblem);
            
            // 步骤2: 重用 (Reuse) 
            const adaptedSolution = await this.reuse(similarCases, newProblem);
            
            // 步骤3: 修正 (Revise)
            const revisedSolution = await this.revise(adaptedSolution, newProblem);
            
            // 步骤4: 保留 (Retain)
            await this.retain(newProblem, revisedSolution);
            
            return revisedSolution;
        } catch (error) {
            console.error('CBR推理过程出错:', error);
            throw error;
        }
    }

    // 步骤1: 检索相似案例
    async retrieve(newProblem) {
        console.log('🔍 开始检索阶段...');
        
        // 获取相关案例库
        const relevantCases = this.getCasesByDomain(newProblem.domain);
        
        // 计算相似度
        const similarities = [];
        for (const case_ of relevantCases) {
            const similarity = this.similarityEngine.calculate(newProblem, case_);
            similarities.push({
                case: case_,
                similarity: similarity,
                features: this.similarityEngine.getMatchingFeatures(newProblem, case_)
            });
        }
        
        // 按相似度排序，返回最相似的案例
        const sortedCases = similarities
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, 5); // 取前5个最相似的案例
            
        console.log(\`检索到 \${sortedCases.length} 个相似案例\`);
        return sortedCases;
    }

    // 步骤2: 重用案例解决方案
    async reuse(similarCases, newProblem) {
        console.log('🔄 开始重用阶段...');
        
        if (similarCases.length === 0) {
            throw new Error('没有找到相似案例');
        }
        
        // 选择最相似的案例作为基础
        const bestCase = similarCases[0];
        
        // 案例适应：调整历史解决方案以适应新问题
        const adaptedSolution = this.adaptationEngine.adapt(
            bestCase.case.solution,
            bestCase.case.problem,
            newProblem,
            bestCase.features
        );
        
        // 融合多个相似案例的解决方案（可选）
        if (similarCases.length > 1) {
            const fusedSolution = this.adaptationEngine.fuseSolutions(
                similarCases.map(sc => sc.case.solution),
                similarCases.map(sc => sc.similarity)
            );
            adaptedSolution.alternatives = fusedSolution;
        }
        
        console.log('重用完成，生成适应性解决方案');
        return adaptedSolution;
    }

    // 步骤3: 修正解决方案
    async revise(solution, problem) {
        console.log('⚙️ 开始修正阶段...');
        
        // 解决方案验证
        const validation = await this.validateSolution(solution, problem);
        
        if (validation.isValid) {
            console.log('解决方案验证通过');
            return {
                ...solution,
                confidence: validation.confidence,
                validationResult: validation
            };
        } else {
            console.log('解决方案需要修正');
            
            // 根据验证结果修正解决方案
            const revisedSolution = await this.correctSolution(
                solution, 
                problem, 
                validation.issues
            );
            
            return {
                ...revisedSolution,
                confidence: validation.confidence * 0.8, // 修正后置信度降低
                revisionHistory: [solution], // 保留修正历史
                validationResult: validation
            };
        }
    }

    // 步骤4: 保留新案例
    async retain(problem, solution) {
        console.log('💾 开始保留阶段...');
        
        // 创建新案例
        const newCase = {
            id: this.generateCaseId(),
            problem: problem,
            solution: solution,
            timestamp: new Date(),
            domain: problem.domain,
            features: this.extractFeatures(problem),
            outcome: 'pending', // 初始状态为待验证
            usageCount: 0,
            successRate: 0
        };
        
        // 案例质量评估
        const quality = this.learningEngine.assessCaseQuality(newCase);
        
        if (quality.shouldRetain) {
            // 添加到案例库
            this.addCaseToLibrary(newCase);
            
            // 更新案例库索引
            this.updateCaseIndex(newCase);
            
            // 案例库维护（删除过时或低质量案例）
            await this.maintainCaseLibrary(problem.domain);
            
            console.log(\`新案例已保留，案例ID: \${newCase.id}\`);
        } else {
            console.log('案例质量不足，未保留');
        }
        
        return newCase;
    }
}

// 2. 相似度计算引擎
class SimilarityEngine {
    constructor() {
        this.weights = {
            structural: 0.4,    // 结构相似度权重
            semantic: 0.4,      // 语义相似度权重
            contextual: 0.2     // 上下文相似度权重
        };
    }

    // 计算两个案例的相似度
    calculate(problem1, problem2) {
        // 结构相似度：比较问题的结构特征
        const structuralSim = this.calculateStructuralSimilarity(problem1, problem2);
        
        // 语义相似度：比较问题的语义内容
        const semanticSim = this.calculateSemanticSimilarity(problem1, problem2);
        
        // 上下文相似度：比较问题的上下文环境
        const contextualSim = this.calculateContextualSimilarity(problem1, problem2);
        
        // 加权计算总相似度
        const totalSimilarity = 
            structuralSim * this.weights.structural +
            semanticSim * this.weights.semantic +
            contextualSim * this.weights.contextual;
            
        return Math.min(Math.max(totalSimilarity, 0), 1); // 确保在[0,1]范围内
    }

    // 结构相似度计算
    calculateStructuralSimilarity(p1, p2) {
        const features1 = this.extractStructuralFeatures(p1);
        const features2 = this.extractStructuralFeatures(p2);
        
        // 使用Jaccard相似度
        const intersection = features1.filter(f => features2.includes(f)).length;
        const union = new Set([...features1, ...features2]).size;
        
        return union > 0 ? intersection / union : 0;
    }

    // 语义相似度计算
    calculateSemanticSimilarity(p1, p2) {
        // 简化的语义相似度计算（实际应用中可使用词向量、BERT等）
        const text1 = this.extractTextContent(p1).toLowerCase();
        const text2 = this.extractTextContent(p2).toLowerCase();
        
        const words1 = new Set(text1.split(/\\s+/));
        const words2 = new Set(text2.split(/\\s+/));
        
        const intersection = [...words1].filter(w => words2.has(w)).length;
        const union = new Set([...words1, ...words2]).size;
        
        return union > 0 ? intersection / union : 0;
    }

    // 上下文相似度计算
    calculateContextualSimilarity(p1, p2) {
        // 比较上下文特征（如时间、地点、用户类型等）
        const context1 = p1.context || {};
        const context2 = p2.context || {};
        
        const contextKeys = new Set([...Object.keys(context1), ...Object.keys(context2)]);
        let matchCount = 0;
        
        for (const key of contextKeys) {
            if (context1[key] === context2[key]) {
                matchCount++;
            }
        }
        
        return contextKeys.size > 0 ? matchCount / contextKeys.size : 0;
    }

    // 获取匹配的特征
    getMatchingFeatures(p1, p2) {
        const features1 = this.extractStructuralFeatures(p1);
        const features2 = this.extractStructuralFeatures(p2);
        
        return features1.filter(f => features2.includes(f));
    }
}

// 3. 案例适应引擎
class AdaptationEngine {
    // 适应案例解决方案
    adapt(oldSolution, oldProblem, newProblem, matchingFeatures) {
        console.log('开始案例适应...');
        
        // 识别需要适应的差异
        const differences = this.identifyDifferences(oldProblem, newProblem);
        
        // 应用适应策略
        let adaptedSolution = { ...oldSolution };
        
        for (const diff of differences) {
            adaptedSolution = this.applyAdaptationRule(
                adaptedSolution, 
                diff, 
                matchingFeatures
            );
        }
        
        // 添加适应元信息
        adaptedSolution.adaptationInfo = {
            baseCaseId: oldSolution.caseId,
            adaptationRules: differences.map(d => d.rule),
            confidence: this.calculateAdaptationConfidence(differences, matchingFeatures)
        };
        
        return adaptedSolution;
    }

    // 识别问题间的差异
    identifyDifferences(oldProblem, newProblem) {
        const differences = [];
        
        // 比较关键特征
        const oldFeatures = this.extractFeatures(oldProblem);
        const newFeatures = this.extractFeatures(newProblem);
        
        for (const feature in newFeatures) {
            if (oldFeatures[feature] !== newFeatures[feature]) {
                differences.push({
                    type: 'feature_change',
                    feature: feature,
                    oldValue: oldFeatures[feature],
                    newValue: newFeatures[feature],
                    rule: this.selectAdaptationRule(feature, oldFeatures[feature], newFeatures[feature])
                });
            }
        }
        
        return differences;
    }

    // 应用适应规则
    applyAdaptationRule(solution, difference, matchingFeatures) {
        switch (difference.rule) {
            case 'parameter_substitution':
                return this.substituteParameters(solution, difference);
            case 'component_replacement':
                return this.replaceComponents(solution, difference);
            case 'constraint_relaxation':
                return this.relaxConstraints(solution, difference);
            case 'method_modification':
                return this.modifyMethod(solution, difference);
            default:
                console.warn(\`未知的适应规则: \${difference.rule}\`);
                return solution;
        }
    }

    // 融合多个解决方案
    fuseSolutions(solutions, weights) {
        console.log('融合多个解决方案...');
        
        const fusedSolution = {
            type: 'fused_solution',
            components: [],
            confidence: 0
        };
        
        // 加权融合
        for (let i = 0; i < solutions.length; i++) {
            const solution = solutions[i];
            const weight = weights[i];
            
            fusedSolution.components.push({
                solution: solution,
                weight: weight,
                contribution: this.calculateContribution(solution, weight)
            });
            
            fusedSolution.confidence += solution.confidence * weight;
        }
        
        // 归一化置信度
        const totalWeight = weights.reduce((sum, w) => sum + w, 0);
        fusedSolution.confidence /= totalWeight;
        
        return fusedSolution;
    }
}

// 4. 学习引擎
class LearningEngine {
    constructor() {
        this.performanceHistory = [];
        this.adaptationRules = new Map();
    }

    // 评估案例质量
    assessCaseQuality(case_) {
        const quality = {
            novelty: this.calculateNovelty(case_),
            completeness: this.calculateCompleteness(case_),
            reliability: this.calculateReliability(case_),
            shouldRetain: false
        };
        
        // 综合评分
        const overallScore = (quality.novelty + quality.completeness + quality.reliability) / 3;
        quality.overallScore = overallScore;
        quality.shouldRetain = overallScore > 0.6; // 阈值可调
        
        return quality;
    }

    // 计算案例新颖性
    calculateNovelty(case_) {
        // 检查案例库中是否有相似案例
        const similarCases = this.findSimilarCases(case_, 0.8); // 80%相似度阈值
        
        if (similarCases.length === 0) {
            return 1.0; // 完全新颖
        } else {
            return Math.max(0, 1 - (similarCases.length / 10)); // 相似案例越多，新颖性越低
        }
    }

    // 更新系统性能
    updatePerformance(caseId, outcome, feedback) {
        this.performanceHistory.push({
            caseId: caseId,
            outcome: outcome,
            feedback: feedback,
            timestamp: new Date()
        });
        
        // 更新适应规则的效果
        this.updateAdaptationRuleEffectiveness(caseId, outcome);
        
        // 触发学习过程
        this.triggerLearning();
    }

    // 触发增量学习
    triggerLearning() {
        // 分析最近的性能数据
        const recentPerformance = this.performanceHistory.slice(-100); // 最近100个案例
        
        // 识别模式和趋势
        const patterns = this.identifyPerformancePatterns(recentPerformance);
        
        // 调整系统参数
        this.adjustSystemParameters(patterns);
        
        console.log('系统学习完成，参数已更新');
    }
}

// 5. 使用示例
async function demonstrateCBR() {
    // 创建CBR系统
    const cbrSystem = new CBRSystem();
    
    // 定义新问题
    const newProblem = {
        domain: 'medical',
        symptoms: ['发热', '咳嗽', '胸痛'],
        patientAge: 35,
        duration: '3天',
        context: {
            season: '冬季',
            location: '北京',
            patientType: '成人'
        }
    };
    
    try {
        // 执行CBR推理
        console.log('开始CBR推理过程...');
        const solution = await cbrSystem.performCBR(newProblem);
        
        console.log('CBR推理完成！');
        console.log('解决方案:', solution);
        
        // 模拟用户反馈
        setTimeout(() => {
            cbrSystem.learningEngine.updatePerformance(
                solution.caseId, 
                'success', 
                { rating: 4.5, comments: '诊断准确' }
            );
        }, 5000);
        
    } catch (error) {
        console.error('CBR推理失败:', error);
    }
}

// ===== CBR系统的核心优势 =====

/*
1. 经验驱动学习
   - 基于历史成功案例进行推理
   - 避免从零开始解决问题
   - 利用领域专家的经验知识

2. 增量学习能力
   - 每次解决问题都积累新经验
   - 系统性能随使用而提升
   - 适应环境变化和新需求

3. 解释性强
   - 可以追溯推理过程
   - 展示相似案例作为依据
   - 便于用户理解和信任

4. 领域适应性
   - 适用于多种问题领域
   - 可以处理不完整或模糊信息
   - 支持复杂的现实世界问题

5. 知识获取容易
   - 直接从案例中学习
   - 无需复杂的知识工程
   - 可以利用现有的案例数据
*/

// ===== CBR与其他AI方法的比较 =====

/*
CBR vs 规则系统:
- CBR: 基于案例，灵活适应
- 规则系统: 基于规则，逻辑严格

CBR vs 机器学习:
- CBR: 可解释，增量学习
- 机器学习: 统计模式，需大量数据

CBR vs 专家系统:
- CBR: 经验驱动，易维护
- 专家系统: 知识驱动，难更新
*/
`;
}
