// 插件扩展机制演示 - 主要逻辑

class PluginSystem {
    constructor() {
        this.installedPlugins = new Map();
        this.pluginRegistry = new Map();
        this.eventBus = new EventBus();
        this.logCount = 1;
        this.initializeSystem();
        this.setupEventListeners();
    }

    initializeSystem() {
        // 注册插件接口
        this.registerPluginInterfaces();
        this.log('SYSTEM', '插件管理器初始化完成');
        this.log('SYSTEM', '接口注册中心就绪');
        this.log('SYSTEM', '事件总线启动');
    }

    registerPluginInterfaces() {
        // 定义标准插件接口
        this.pluginRegistry.set('ITranslator', {
            name: '翻译接口',
            methods: ['translate', 'getSupportedLanguages'],
            description: '提供多语言翻译功能的标准接口'
        });

        this.pluginRegistry.set('ICalculator', {
            name: '计算器接口',
            methods: ['calculate', 'getOperations'],
            description: '提供数学计算功能的标准接口'
        });

        this.pluginRegistry.set('IWeather', {
            name: '天气接口',
            methods: ['getCurrentWeather', 'getForecast'],
            description: '提供天气查询功能的标准接口'
        });

        this.pluginRegistry.set('ITimer', {
            name: '定时器接口',
            methods: ['setTimer', 'setAlarm', 'stopTimer'],
            description: '提供时间管理功能的标准接口'
        });

        this.pluginRegistry.set('ITextEditor', {
            name: '文本编辑接口',
            methods: ['createNote', 'editNote', 'saveNote'],
            description: '提供文本编辑功能的标准接口'
        });

        this.pluginRegistry.set('IImageProcessor', {
            name: '图像处理接口',
            methods: ['applyFilter', 'resize', 'crop'],
            description: '提供图像处理功能的标准接口'
        });
    }

    setupEventListeners() {
        // 设置拖拽事件
        this.setupDragAndDrop();
        
        // 设置其他UI事件
        this.setupUIEvents();
    }

    setupDragAndDrop() {
        const plugins = document.querySelectorAll('.plugin');
        const dropZone = document.getElementById('dropZone');

        plugins.forEach(plugin => {
            plugin.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', plugin.dataset.plugin);
                plugin.style.opacity = '0.5';
            });

            plugin.addEventListener('dragend', (e) => {
                plugin.style.opacity = '1';
            });
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
        });

        dropZone.addEventListener('dragleave', (e) => {
            dropZone.classList.remove('drag-over');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            
            const pluginId = e.dataTransfer.getData('text/plain');
            this.installPlugin(pluginId);
        });
    }

    setupUIEvents() {
        // Tab切换事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn')) {
                this.switchTab(e.target.dataset.tab);
            }
        });
    }

    installPlugin(pluginId) {
        if (this.installedPlugins.has(pluginId)) {
            this.log('ERROR', `插件 ${pluginId} 已经安装，无法重复安装`);
            return;
        }

        // 获取插件信息
        const pluginElement = document.querySelector(`[data-plugin="${pluginId}"]`);
        const pluginName = pluginElement.querySelector('.plugin-name').textContent;
        const pluginIcon = pluginElement.querySelector('.plugin-icon').textContent;
        const pluginInterface = pluginElement.dataset.interface;

        // 模拟插件加载过程
        this.log('PLUGIN', `开始安装插件: ${pluginName}`);
        this.log('PLUGIN', `验证接口兼容性: ${pluginInterface}`);
        
        setTimeout(() => {
            // 创建插件实例
            const plugin = this.createPluginInstance(pluginId, pluginName, pluginIcon, pluginInterface);
            
            // 注册插件
            this.installedPlugins.set(pluginId, plugin);
            
            // 更新UI
            this.updateInstalledPluginsList();
            this.updateSystemStatus();
            this.addPluginTab(pluginId, pluginName, pluginIcon);
            
            this.log('PLUGIN', `插件 ${pluginName} 安装成功`);
            this.log('SYSTEM', `系统功能已扩展，新增 ${pluginName} 能力`);
            
            // 触发插件安装事件
            this.eventBus.emit('pluginInstalled', { pluginId, pluginName });
        }, 1000);
    }

    createPluginInstance(pluginId, pluginName, pluginIcon, pluginInterface) {
        const interfaceInfo = this.pluginRegistry.get(pluginInterface);
        
        return {
            id: pluginId,
            name: pluginName,
            icon: pluginIcon,
            interface: pluginInterface,
            methods: interfaceInfo.methods,
            isActive: true,
            version: document.querySelector(`[data-plugin="${pluginId}"] .plugin-version`).textContent
        };
    }

    uninstallPlugin(pluginId) {
        if (!this.installedPlugins.has(pluginId)) {
            return;
        }

        const plugin = this.installedPlugins.get(pluginId);
        
        // 移除插件
        this.installedPlugins.delete(pluginId);
        
        // 更新UI
        this.updateInstalledPluginsList();
        this.updateSystemStatus();
        this.removePluginTab(pluginId);
        
        this.log('PLUGIN', `插件 ${plugin.name} 已卸载`);
        this.log('SYSTEM', `系统功能已更新`);
        
        // 触发插件卸载事件
        this.eventBus.emit('pluginUninstalled', { pluginId, pluginName: plugin.name });
    }

    updateInstalledPluginsList() {
        const pluginList = document.getElementById('pluginList');
        
        if (this.installedPlugins.size === 0) {
            pluginList.innerHTML = '<div class="empty-state">暂无插件，拖拽插件到上方区域进行安装</div>';
            return;
        }

        let html = '';
        this.installedPlugins.forEach((plugin, pluginId) => {
            html += `
                <div class="installed-plugin">
                    <div class="plugin-info">
                        <span>${plugin.icon}</span>
                        <span>${plugin.name}</span>
                        <span style="font-size: 0.8em; opacity: 0.8;">${plugin.version}</span>
                    </div>
                    <button class="uninstall-btn" onclick="pluginSystem.uninstallPlugin('${pluginId}')">
                        卸载
                    </button>
                </div>
            `;
        });
        
        pluginList.innerHTML = html;
    }

    updateSystemStatus() {
        const statusElement = document.getElementById('systemStatus');
        const pluginCount = this.installedPlugins.size;
        
        if (pluginCount === 0) {
            statusElement.textContent = '基础系统运行中 - 等待插件扩展...';
        } else {
            statusElement.textContent = `增强系统运行中 - 已加载 ${pluginCount} 个插件模块`;
        }
    }

    addPluginTab(pluginId, pluginName, pluginIcon) {
        const tabButtons = document.getElementById('tabButtons');
        const tabContent = document.getElementById('tabContent');

        // 添加标签按钮
        const tabBtn = document.createElement('button');
        tabBtn.className = 'tab-btn';
        tabBtn.dataset.tab = pluginId;
        tabBtn.textContent = `${pluginIcon} ${pluginName}`;
        tabButtons.appendChild(tabBtn);

        // 添加标签内容
        const tabPane = document.createElement('div');
        tabPane.className = 'tab-pane';
        tabPane.id = pluginId;
        tabPane.innerHTML = this.generatePluginInterface(pluginId);
        tabContent.appendChild(tabPane);
    }

    removePluginTab(pluginId) {
        // 移除标签按钮
        const tabBtn = document.querySelector(`[data-tab="${pluginId}"]`);
        if (tabBtn) {
            tabBtn.remove();
        }

        // 移除标签内容
        const tabPane = document.getElementById(pluginId);
        if (tabPane) {
            tabPane.remove();
        }

        // 如果当前显示的是被移除的标签，切换到概览
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab || activeTab.id === pluginId) {
            this.switchTab('overview');
        }
    }

    generatePluginInterface(pluginId) {
        const plugin = this.installedPlugins.get(pluginId);
        const interfaceInfo = this.pluginRegistry.get(plugin.interface);

        return `
            <div class="plugin-interface">
                <div class="interface-header">
                    <h4>${plugin.icon} ${plugin.name} 功能测试</h4>
                    <p>接口: ${interfaceInfo.name}</p>
                    <p class="interface-desc">${interfaceInfo.description}</p>
                </div>
                <div class="interface-methods">
                    <h5>可用方法:</h5>
                    ${interfaceInfo.methods.map(method => `
                        <div class="method-item">
                            <button class="method-btn" onclick="pluginSystem.callPluginMethod('${pluginId}', '${method}')">
                                ${method}()
                            </button>
                        </div>
                    `).join('')}
                </div>
                <div class="test-result" id="result-${pluginId}">
                    <p>点击上方方法按钮进行功能测试</p>
                </div>
            </div>
        `;
    }

    callPluginMethod(pluginId, methodName) {
        const plugin = this.installedPlugins.get(pluginId);
        const resultDiv = document.getElementById(`result-${pluginId}`);
        
        // 模拟方法调用
        resultDiv.innerHTML = `<p>正在调用 ${plugin.name}.${methodName}()...</p>`;
        
        setTimeout(() => {
            const mockResult = this.generateMockResult(pluginId, methodName);
            resultDiv.innerHTML = `
                <h6>调用结果:</h6>
                <div class="result-content">${mockResult}</div>
            `;
            
            this.log('PLUGIN', `调用 ${plugin.name}.${methodName}() 成功`);
        }, 800);
    }

    generateMockResult(pluginId, methodName) {
        const mockResults = {
            translator: {
                translate: '翻译结果: "Hello World" → "你好世界"',
                getSupportedLanguages: '支持语言: 中文、英文、日文、韩文、法文...'
            },
            calculator: {
                calculate: '计算结果: 123 + 456 = 579',
                getOperations: '支持运算: +, -, *, /, %, ^, sqrt, sin, cos...'
            },
            weather: {
                getCurrentWeather: '当前天气: 北京 晴天 25°C 湿度60% 东南风2级',
                getForecast: '未来7天: 晴→多云→小雨→晴→多云→晴→晴'
            },
            timer: {
                setTimer: '定时器已设置: 25分钟后提醒',
                setAlarm: '闹钟已设置: 明天早上7:00',
                stopTimer: '定时器已停止'
            },
            notepad: {
                createNote: '新建笔记: "学习插件机制.txt" 创建成功',
                editNote: '编辑笔记: 内容已更新并自动保存',
                saveNote: '保存笔记: 已保存到本地存储'
            },
            imageProcessor: {
                applyFilter: '滤镜效果: 已应用"复古"滤镜效果',
                resize: '图片缩放: 已调整为 800x600 像素',
                crop: '图片裁剪: 已裁剪为 16:9 比例'
            }
        };

        return mockResults[pluginId]?.[methodName] || '方法执行成功';
    }

    switchTab(tabId) {
        // 移除所有活动状态
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

        // 激活选中的标签
        const targetBtn = document.querySelector(`[data-tab="${tabId}"]`);
        const targetPane = document.getElementById(tabId);

        if (targetBtn && targetPane) {
            targetBtn.classList.add('active');
            targetPane.classList.add('active');
        }
    }

    log(type, message) {
        const logContent = document.getElementById('logContent');
        const logCount = document.getElementById('logCount');
        
        const time = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `
            <span class="log-time">[${time}]</span>
            <span class="log-type">[${type}]</span>
            <span class="log-message">${message}</span>
        `;
        
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
        
        this.logCount++;
        logCount.textContent = this.logCount;
    }
}

// 事件总线类
class EventBus {
    constructor() {
        this.events = {};
    }

    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }

    off(event, callback) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        }
    }
}

// 全局函数
function clearLog() {
    const logContent = document.getElementById('logContent');
    const logCount = document.getElementById('logCount');
    
    logContent.innerHTML = '';
    pluginSystem.logCount = 0;
    logCount.textContent = '0';
    
    pluginSystem.log('SYSTEM', '日志已清空');
}

function showCoreCode() {
    const modal = document.getElementById('codeModal');
    const codeDisplay = document.getElementById('codeDisplay');
    
    // 从code.js获取代码内容
    codeDisplay.textContent = getCoreCode();
    modal.style.display = 'block';
}

function closeCoreCode() {
    const modal = document.getElementById('codeModal');
    modal.style.display = 'none';
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('codeModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// 初始化系统
let pluginSystem;

document.addEventListener('DOMContentLoaded', function() {
    pluginSystem = new PluginSystem();
    
    // 添加一些样式增强
    addStyleEnhancements();
});

function addStyleEnhancements() {
    // 为日志类型添加颜色
    const style = document.createElement('style');
    style.textContent = `
        .log-entry .log-type:contains("SYSTEM") { color: #00bfff !important; }
        .log-entry .log-type:contains("PLUGIN") { color: #ffa500 !important; }
        .log-entry .log-type:contains("ERROR") { color: #ff4444 !important; }
        
        .plugin-interface {
            padding: 20px;
            background: #f8f9ff;
            border-radius: 10px;
        }
        
        .interface-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        
        .interface-header h4 {
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .interface-desc {
            color: #666;
            font-style: italic;
        }
        
        .interface-methods {
            margin-bottom: 20px;
        }
        
        .interface-methods h5 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .method-item {
            margin: 8px 0;
        }
        
        .method-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            transition: background 0.3s ease;
        }
        
        .method-btn:hover {
            background: #5a6fd8;
        }
        
        .test-result {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .result-content {
            background: #1a1a1a;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin-top: 8px;
        }
    `;
    document.head.appendChild(style);
}
