// 核心代码展示 - 插件扩展机制实现

function getCoreCode() {
    return `
// ===== 插件扩展机制核心实现代码 =====

// 1. 插件接口定义 (Interface Definition)
interface IPlugin {
    id: string;
    name: string;
    version: string;
    interface: string;
    
    // 生命周期方法
    initialize(): void;
    activate(): void;
    deactivate(): void;
    destroy(): void;
}

// 2. 插件管理器 (Plugin Manager)
class PluginManager {
    private plugins: Map<string, IPlugin> = new Map();
    private interfaces: Map<string, PluginInterface> = new Map();
    private eventBus: EventBus = new EventBus();
    
    // 注册插件接口
    registerInterface(name: string, interface: PluginInterface): void {
        this.interfaces.set(name, interface);
        this.log('接口注册', \`已注册接口: \${name}\`);
    }
    
    // 安装插件 (核心方法)
    async installPlugin(pluginConfig: PluginConfig): Promise<boolean> {
        try {
            // 1. 验证接口兼容性
            if (!this.validateInterface(pluginConfig)) {
                throw new Error('接口不兼容');
            }
            
            // 2. 动态加载插件代码
            const pluginModule = await this.loadPluginModule(pluginConfig.path);
            
            // 3. 创建插件实例
            const plugin = new pluginModule.default(pluginConfig);
            
            // 4. 依赖注入
            this.injectDependencies(plugin);
            
            // 5. 初始化插件
            await plugin.initialize();
            
            // 6. 注册到系统
            this.plugins.set(plugin.id, plugin);
            
            // 7. 激活插件
            await plugin.activate();
            
            // 8. 触发事件
            this.eventBus.emit('pluginInstalled', { plugin });
            
            this.log('插件安装', \`插件 \${plugin.name} 安装成功\`);
            return true;
            
        } catch (error) {
            this.log('错误', \`插件安装失败: \${error.message}\`);
            return false;
        }
    }
    
    // 卸载插件
    async uninstallPlugin(pluginId: string): Promise<boolean> {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) return false;
        
        try {
            // 1. 停用插件
            await plugin.deactivate();
            
            // 2. 清理资源
            await plugin.destroy();
            
            // 3. 从系统移除
            this.plugins.delete(pluginId);
            
            // 4. 触发事件
            this.eventBus.emit('pluginUninstalled', { pluginId });
            
            this.log('插件卸载', \`插件 \${plugin.name} 已卸载\`);
            return true;
            
        } catch (error) {
            this.log('错误', \`插件卸载失败: \${error.message}\`);
            return false;
        }
    }
    
    // 接口验证 (关键安全机制)
    private validateInterface(config: PluginConfig): boolean {
        const requiredInterface = this.interfaces.get(config.interface);
        if (!requiredInterface) {
            return false;
        }
        
        // 验证必需方法
        for (const method of requiredInterface.requiredMethods) {
            if (!config.exports.includes(method)) {
                return false;
            }
        }
        
        return true;
    }
    
    // 动态模块加载
    private async loadPluginModule(path: string): Promise<any> {
        // 沙箱环境中加载插件代码
        const sandbox = this.createSandbox();
        return await sandbox.import(path);
    }
    
    // 依赖注入 (IoC容器)
    private injectDependencies(plugin: IPlugin): void {
        // 注入系统服务
        plugin.eventBus = this.eventBus;
        plugin.logger = this.createLogger(plugin.id);
        plugin.storage = this.createStorage(plugin.id);
        
        // 注入其他插件服务 (如果需要)
        plugin.services = this.getAvailableServices();
    }
    
    // 创建安全沙箱
    private createSandbox(): Sandbox {
        return new Sandbox({
            // 限制访问权限
            allowedGlobals: ['console', 'setTimeout', 'clearTimeout'],
            // 禁止危险操作
            blockedAPIs: ['eval', 'Function', 'XMLHttpRequest'],
            // 资源限制
            memoryLimit: '50MB',
            timeoutLimit: 5000
        });
    }
}

// 3. 事件总线 (Event Bus) - 插件间通信
class EventBus {
    private listeners: Map<string, Function[]> = new Map();
    
    // 订阅事件
    on(event: string, callback: Function): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)!.push(callback);
    }
    
    // 发布事件
    emit(event: string, data: any): void {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(\`事件处理错误: \${error.message}\`);
                }
            });
        }
    }
    
    // 取消订阅
    off(event: string, callback: Function): void {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }
}

// 4. 插件基类 (Plugin Base Class)
abstract class BasePlugin implements IPlugin {
    public id: string;
    public name: string;
    public version: string;
    public interface: string;
    
    // 注入的依赖
    protected eventBus: EventBus;
    protected logger: Logger;
    protected storage: Storage;
    protected services: ServiceRegistry;
    
    constructor(config: PluginConfig) {
        this.id = config.id;
        this.name = config.name;
        this.version = config.version;
        this.interface = config.interface;
    }
    
    // 生命周期方法 (需要子类实现)
    abstract async initialize(): Promise<void>;
    abstract async activate(): Promise<void>;
    abstract async deactivate(): Promise<void>;
    abstract async destroy(): Promise<void>;
    
    // 通用工具方法
    protected log(message: string): void {
        this.logger.info(\`[\${this.name}] \${message}\`);
    }
    
    protected emitEvent(event: string, data: any): void {
        this.eventBus.emit(\`plugin:\${this.id}:\${event}\`, data);
    }
    
    protected subscribeEvent(event: string, callback: Function): void {
        this.eventBus.on(event, callback);
    }
}

// 5. 具体插件实现示例
class TranslatorPlugin extends BasePlugin {
    private supportedLanguages: string[] = ['zh', 'en', 'ja', 'ko'];
    
    async initialize(): Promise<void> {
        this.log('翻译插件初始化中...');
        // 加载语言模型
        await this.loadLanguageModels();
    }
    
    async activate(): Promise<void> {
        this.log('翻译插件已激活');
        // 注册翻译服务
        this.services.register('translator', this);
        
        // 监听翻译请求
        this.subscribeEvent('translate:request', this.handleTranslateRequest.bind(this));
    }
    
    async deactivate(): Promise<void> {
        this.log('翻译插件已停用');
        this.services.unregister('translator');
    }
    
    async destroy(): Promise<void> {
        this.log('翻译插件资源清理完成');
        // 清理缓存、关闭连接等
    }
    
    // 插件特有方法
    async translate(text: string, from: string, to: string): Promise<string> {
        this.log(\`翻译请求: \${text} (\${from} -> \${to})\`);
        
        // 模拟翻译逻辑
        const result = await this.performTranslation(text, from, to);
        
        this.emitEvent('translated', { text, result, from, to });
        return result;
    }
    
    getSupportedLanguages(): string[] {
        return [...this.supportedLanguages];
    }
    
    private async loadLanguageModels(): Promise<void> {
        // 模拟加载语言模型
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    private async performTranslation(text: string, from: string, to: string): Promise<string> {
        // 模拟翻译API调用
        return \`[翻译结果] \${text}\`;
    }
    
    private handleTranslateRequest(data: any): void {
        this.translate(data.text, data.from, data.to);
    }
}

// 6. 使用示例
const pluginManager = new PluginManager();

// 注册接口
pluginManager.registerInterface('ITranslator', {
    name: '翻译接口',
    requiredMethods: ['translate', 'getSupportedLanguages'],
    optionalMethods: ['detectLanguage']
});

// 安装插件
pluginManager.installPlugin({
    id: 'translator',
    name: '翻译插件',
    version: '1.0.0',
    interface: 'ITranslator',
    path: './plugins/translator.js',
    exports: ['translate', 'getSupportedLanguages']
});

// ===== 核心设计原则 =====

/*
1. 开闭原则 (Open-Closed Principle)
   - 对扩展开放：可以添加新插件
   - 对修改封闭：不需要修改核心代码

2. 依赖倒置原则 (Dependency Inversion)
   - 高层模块不依赖低层模块
   - 都依赖于抽象接口

3. 单一职责原则 (Single Responsibility)
   - 每个插件只负责一个功能领域
   - 插件管理器只负责插件生命周期

4. 接口隔离原则 (Interface Segregation)
   - 插件只需要实现必要的接口方法
   - 避免臃肿的接口设计

5. 控制反转 (Inversion of Control)
   - 通过依赖注入提供系统服务
   - 插件不直接创建依赖对象
*/

// ===== 安全机制 =====

/*
1. 沙箱隔离
   - 限制插件访问系统资源
   - 防止恶意代码执行

2. 接口验证
   - 确保插件符合接口规范
   - 防止不兼容插件安装

3. 资源限制
   - 内存使用限制
   - 执行时间限制

4. 权限控制
   - 基于角色的访问控制
   - API调用权限验证
*/
`;
}
