// 全局变量
let currentStep = 0;
let words = [];
let qMatrix = [];
let kMatrix = [];
let vMatrix = [];
let attentionScores = [];
let finalResults = [];
let selectedWordIndex = -1;

// 步骤说明
const stepExplanations = [
    {
        title: "步骤1: 词语分析",
        explanation: "我们将输入的句子分解为单个词语。每个词语都会被转换为数字向量，这是计算机理解文本的基础。点击任意词语可以查看它与其他词语的关系。"
    },
    {
        title: "步骤2: 生成Q、K、V矩阵",
        explanation: "每个词语被转换为三个不同的向量：查询(Q)表示'我想要什么信息'，键(K)表示'我能提供什么信息'，值(V)表示'实际的信息内容'。这种设计让模型能够灵活地匹配和组合信息。"
    },
    {
        title: "步骤3: 计算注意力分数",
        explanation: "通过计算Q和K的点积，我们得到注意力分数矩阵。每个数值表示一个词对另一个词的关注程度。分数越高，表示关联性越强。这就是'注意力'的数学体现。"
    },
    {
        title: "步骤4: 生成最终结果",
        explanation: "使用注意力权重对值向量进行加权平均，得到每个词的新表示。这个新表示融合了句子中所有相关词语的信息，这就是自注意力机制的核心价值。"
    }
];

// 初始化
document.addEventListener('DOMContentLoaded', function () {
    const analyzeBtn = document.getElementById('analyze-btn');
    const sentenceInput = document.getElementById('sentence-input');
    const prevBtn = document.getElementById('prev-step');
    const nextBtn = document.getElementById('next-step');
    const resetBtn = document.getElementById('reset-demo');

    analyzeBtn.addEventListener('click', startAnalysis);
    prevBtn.addEventListener('click', previousStep);
    nextBtn.addEventListener('click', nextStep);
    resetBtn.addEventListener('click', resetDemo);

    // 回车键触发分析
    sentenceInput.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            startAnalysis();
        }
    });

    // 生成示例注意力矩阵
    generateExampleMatrix();
});

// 开始分析
function startAnalysis() {
    const sentence = document.getElementById('sentence-input').value.trim();
    if (!sentence) {
        alert('请输入一个句子！');
        return;
    }

    // 分词（简单按字符分割中文，按空格分割英文）
    words = sentence.includes(' ') ?
        sentence.split(' ').filter(word => word.trim()) :
        sentence.split('').filter(char => char.trim());

    if (words.length < 2) {
        alert('请输入至少包含2个词语的句子！');
        return;
    }

    // 重置状态
    currentStep = 0;
    selectedWordIndex = -1;

    // 生成模拟的QKV矩阵
    generateQKVMatrices();

    // 计算注意力分数
    calculateAttentionScores();

    // 计算最终结果
    calculateFinalResults();

    // 显示第一步
    showStep(0);

    // 显示控制按钮
    document.getElementById('controls').style.display = 'block';
}

// 生成QKV矩阵（模拟）
function generateQKVMatrices() {
    const dimension = 4; // 简化的向量维度

    qMatrix = [];
    kMatrix = [];
    vMatrix = [];

    for (let i = 0; i < words.length; i++) {
        const qVector = [];
        const kVector = [];
        const vVector = [];

        for (let j = 0; j < dimension; j++) {
            // 生成有意义的模拟数据，让相似词有相似的向量
            const baseValue = (words[i].charCodeAt(0) % 10) / 10;
            qVector.push((baseValue + Math.random() * 0.4 - 0.2).toFixed(2));
            kVector.push((baseValue + Math.random() * 0.4 - 0.2).toFixed(2));
            vVector.push((baseValue + Math.random() * 0.4 - 0.2).toFixed(2));
        }

        qMatrix.push(qVector);
        kMatrix.push(kVector);
        vMatrix.push(vVector);
    }
}

// 计算注意力分数
function calculateAttentionScores() {
    attentionScores = [];

    for (let i = 0; i < words.length; i++) {
        const scores = [];
        let sum = 0;

        // 计算原始分数 (Q * K^T)
        for (let j = 0; j < words.length; j++) {
            let score = 0;
            for (let k = 0; k < qMatrix[i].length; k++) {
                score += parseFloat(qMatrix[i][k]) * parseFloat(kMatrix[j][k]);
            }
            scores.push(Math.exp(score)); // 应用exp为softmax做准备
            sum += Math.exp(score);
        }

        // 应用softmax归一化
        const normalizedScores = scores.map(score => (score / sum).toFixed(3));
        attentionScores.push(normalizedScores);
    }
}

// 计算最终结果
function calculateFinalResults() {
    finalResults = [];

    for (let i = 0; i < words.length; i++) {
        const result = [];

        for (let j = 0; j < vMatrix[0].length; j++) {
            let weightedSum = 0;
            for (let k = 0; k < words.length; k++) {
                weightedSum += parseFloat(attentionScores[i][k]) * parseFloat(vMatrix[k][j]);
            }
            result.push(weightedSum.toFixed(2));
        }

        finalResults.push(result);
    }
}

// 显示指定步骤
function showStep(step) {
    currentStep = step;

    // 更新步骤说明
    document.getElementById('current-step-title').textContent = stepExplanations[step].title;
    document.getElementById('current-step-explanation').textContent = stepExplanations[step].explanation;

    // 隐藏所有区域
    document.getElementById('matrices-container').style.display = 'none';
    document.getElementById('attention-scores').style.display = 'none';
    document.getElementById('final-result').style.display = 'none';

    switch (step) {
        case 0:
            showWords();
            break;
        case 1:
            showWords();
            showQKVMatrices();
            break;
        case 2:
            showWords();
            showQKVMatrices();
            showAttentionScores();
            break;
        case 3:
            showWords();
            showAttentionScores();
            showFinalResults();
            break;
    }

    // 更新按钮状态
    updateButtonStates();
}

// 显示词语
function showWords() {
    const container = document.getElementById('words-container');
    container.innerHTML = '';

    words.forEach((word, index) => {
        const wordCard = document.createElement('div');
        wordCard.className = 'word-card fade-in';
        wordCard.textContent = word;
        wordCard.addEventListener('click', () => selectWord(index));

        if (index === selectedWordIndex) {
            wordCard.classList.add('selected');
        }

        container.appendChild(wordCard);
    });
}

// 选择词语
function selectWord(index) {
    selectedWordIndex = index;
    showWords();

    if (currentStep >= 2) {
        highlightAttentionRow(index);
    }
}

// 显示QKV矩阵
function showQKVMatrices() {
    document.getElementById('matrices-container').style.display = 'block';

    displayMatrix('q-matrix', qMatrix, 'Q');
    displayMatrix('k-matrix', kMatrix, 'K');
    displayMatrix('v-matrix', vMatrix, 'V');

    // 添加延迟以确保DOM更新完成
    setTimeout(() => {
        addTooltips();
    }, 100);
}

// 显示矩阵
function displayMatrix(containerId, matrix, type) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    container.style.gridTemplateColumns = `repeat(${matrix[0].length + 1}, 1fr)`;

    // 添加列标题
    container.appendChild(createMatrixCell('', 'header'));
    for (let j = 0; j < matrix[0].length; j++) {
        container.appendChild(createMatrixCell(`${type}${j + 1}`, 'header'));
    }

    // 添加数据行
    for (let i = 0; i < matrix.length; i++) {
        // 行标题
        container.appendChild(createMatrixCell(words[i], 'header'));

        // 数据
        for (let j = 0; j < matrix[i].length; j++) {
            const cell = createMatrixCell(matrix[i][j], 'data');
            if (selectedWordIndex === i) {
                cell.style.background = '#667eea';
                cell.style.color = 'white';
            }
            container.appendChild(cell);
        }
    }
}

// 创建矩阵单元格
function createMatrixCell(content, type) {
    const cell = document.createElement('div');
    cell.className = 'matrix-cell';
    cell.textContent = content;

    if (type === 'header') {
        cell.style.background = '#e2e8f0';
        cell.style.fontWeight = 'bold';
        cell.style.color = '#4a5568';
    }

    return cell;
}

// 显示注意力分数
function showAttentionScores() {
    document.getElementById('attention-scores').style.display = 'block';

    const container = document.getElementById('attention-matrix');
    container.innerHTML = '';
    container.style.gridTemplateColumns = `repeat(${words.length + 1}, 1fr)`;

    // 添加列标题
    container.appendChild(createAttentionCell('', 'header'));
    words.forEach(word => {
        container.appendChild(createAttentionCell(word, 'header'));
    });

    // 添加数据行
    for (let i = 0; i < attentionScores.length; i++) {
        // 行标题
        container.appendChild(createAttentionCell(words[i], 'header'));

        // 数据
        for (let j = 0; j < attentionScores[i].length; j++) {
            const score = parseFloat(attentionScores[i][j]);
            const cell = createAttentionCell(attentionScores[i][j], 'data');

            // 根据分数设置颜色
            cell.style.background = `rgba(102, 126, 234, ${score})`;
            cell.style.color = score > 0.5 ? 'white' : '#333';

            // 添加点击事件显示详细信息
            cell.addEventListener('click', () => {
                showAttentionDetail(i, j, score);
            });

            container.appendChild(cell);
        }
    }
}

// 创建注意力单元格
function createAttentionCell(content, type) {
    const cell = document.createElement('div');
    cell.className = 'attention-cell';
    cell.textContent = content;

    if (type === 'header') {
        cell.style.background = '#f7fafc';
        cell.style.fontWeight = 'bold';
        cell.style.color = '#4a5568';
    }

    return cell;
}

// 高亮注意力行
function highlightAttentionRow(rowIndex) {
    const cells = document.querySelectorAll('.attention-cell');
    const cols = words.length + 1;

    cells.forEach((cell, index) => {
        const row = Math.floor(index / cols);
        const col = index % cols;

        if (row === rowIndex + 1 && col > 0) { // +1因为有标题行
            cell.style.border = '3px solid #667eea';
            cell.style.transform = 'scale(1.1)';
        } else {
            cell.style.border = 'none';
            cell.style.transform = 'scale(1)';
        }
    });
}

// 显示注意力详情
function showAttentionDetail(i, j, score) {
    const message = `"${words[i]}" 对 "${words[j]}" 的注意力分数: ${score}\n\n` +
        `这表示当处理词语 "${words[i]}" 时，模型会给予词语 "${words[j]}" ${(score * 100).toFixed(1)}% 的关注度。\n\n` +
        `${score > 0.3 ? '这是一个较高的关注度，说明这两个词语在语义上有较强的关联。' :
            '这是一个较低的关注度，说明这两个词语在当前上下文中关联性较弱。'}\n\n` +
        `💡 理解要点：\n` +
        `• 注意力分数是通过 Q·K^T 计算得出的\n` +
        `• 分数经过 softmax 归一化，确保每行和为1\n` +
        `• 高分数意味着强关联，低分数意味着弱关联\n` +
        `• 这就是模型"注意力"的数学体现`;

    alert(message);
}

// 添加实时提示功能
function addTooltips() {
    // 为矩阵单元格添加悬停提示
    document.querySelectorAll('.matrix-cell').forEach((cell) => {
        cell.addEventListener('mouseenter', function () {
            if (this.textContent && !isNaN(this.textContent)) {
                showTooltip(this, `数值: ${this.textContent}\n这是向量的一个维度值`);
            }
        });

        cell.addEventListener('mouseleave', hideTooltip);
    });
}

// 显示提示框
function showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 1000;
        pointer-events: none;
        white-space: pre-line;
        max-width: 200px;
    `;

    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + 'px';
    tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';

    element._tooltip = tooltip;
}

// 隐藏提示框
function hideTooltip() {
    if (this._tooltip) {
        document.body.removeChild(this._tooltip);
        this._tooltip = null;
    }
}

// 显示最终结果
function showFinalResults() {
    document.getElementById('final-result').style.display = 'block';

    const container = document.getElementById('result-matrix');
    container.innerHTML = '';
    container.style.gridTemplateColumns = `repeat(${finalResults[0].length + 1}, 1fr)`;

    // 添加列标题
    container.appendChild(createMatrixCell('词语', 'header'));
    for (let j = 0; j < finalResults[0].length; j++) {
        container.appendChild(createMatrixCell(`特征${j + 1}`, 'header'));
    }

    // 添加数据行
    for (let i = 0; i < finalResults.length; i++) {
        // 行标题
        container.appendChild(createMatrixCell(words[i], 'header'));

        // 数据
        for (let j = 0; j < finalResults[i].length; j++) {
            const cell = createMatrixCell(finalResults[i][j], 'data');
            if (selectedWordIndex === i) {
                cell.style.background = '#48bb78';
                cell.style.color = 'white';
            }
            container.appendChild(cell);
        }
    }
}

// 上一步
function previousStep() {
    if (currentStep > 0) {
        showStep(currentStep - 1);
    }
}

// 下一步
function nextStep() {
    if (currentStep < stepExplanations.length - 1) {
        showStep(currentStep + 1);
    }
}

// 更新按钮状态
function updateButtonStates() {
    const prevBtn = document.getElementById('prev-step');
    const nextBtn = document.getElementById('next-step');

    prevBtn.disabled = currentStep === 0;
    nextBtn.disabled = currentStep === stepExplanations.length - 1;

    prevBtn.style.opacity = prevBtn.disabled ? '0.5' : '1';
    nextBtn.style.opacity = nextBtn.disabled ? '0.5' : '1';
}

// 重置演示
function resetDemo() {
    currentStep = 0;
    selectedWordIndex = -1;
    words = [];
    qMatrix = [];
    kMatrix = [];
    vMatrix = [];
    attentionScores = [];
    finalResults = [];

    // 隐藏所有区域
    document.getElementById('words-container').innerHTML = '';
    document.getElementById('matrices-container').style.display = 'none';
    document.getElementById('attention-scores').style.display = 'none';
    document.getElementById('final-result').style.display = 'none';
    document.getElementById('controls').style.display = 'none';

    // 重置步骤说明
    document.getElementById('current-step-title').textContent = '准备开始...';
    document.getElementById('current-step-explanation').textContent = '点击"开始分析"按钮来体验注意力机制的工作过程';

    // 清空输入框
    document.getElementById('sentence-input').value = '我爱学习人工智能';
}

// 生成示例注意力矩阵
function generateExampleMatrix() {
    const exampleWords = ['我', '打开', '了', 'Windows', '准备', '写', 'Python', '脚本'];
    const container = document.getElementById('example-matrix');

    // 预定义的注意力分数矩阵（8x8）
    const attentionMatrix = [
        // 行：我, 打开, 了, Windows, 准备, 写, Python, 脚本
        [0.15, 0.12, 0.08, 0.10, 0.13, 0.14, 0.14, 0.14], // 我
        [0.12, 0.18, 0.10, 0.15, 0.15, 0.15, 0.08, 0.07], // 打开
        [0.08, 0.10, 0.25, 0.05, 0.12, 0.15, 0.12, 0.13], // 了
        [0.10, 0.15, 0.05, 0.20, 0.10, 0.15, 0.85, 0.90], // Windows (重点行)
        [0.13, 0.15, 0.12, 0.10, 0.20, 0.18, 0.06, 0.06], // 准备
        [0.14, 0.15, 0.15, 0.15, 0.18, 0.23, 0.75, 0.80], // 写
        [0.14, 0.08, 0.12, 0.85, 0.06, 0.75, 0.25, 0.88], // Python
        [0.14, 0.07, 0.13, 0.90, 0.06, 0.80, 0.88, 0.30]  // 脚本
    ];

    container.innerHTML = '';

    // 添加空白角落
    const cornerCell = document.createElement('div');
    cornerCell.className = 'example-cell header';
    cornerCell.textContent = '';
    container.appendChild(cornerCell);

    // 添加列标题
    exampleWords.forEach(word => {
        const headerCell = document.createElement('div');
        headerCell.className = 'example-cell header';
        headerCell.textContent = word;
        container.appendChild(headerCell);
    });

    // 添加数据行
    exampleWords.forEach((rowWord, i) => {
        // 行标题
        const rowHeaderCell = document.createElement('div');
        rowHeaderCell.className = 'example-cell header';
        rowHeaderCell.textContent = rowWord;
        container.appendChild(rowHeaderCell);

        // 数据单元格
        exampleWords.forEach((colWord, j) => {
            const score = attentionMatrix[i][j];
            const cell = document.createElement('div');
            cell.className = 'example-cell';
            cell.textContent = score.toFixed(2);

            // 根据分数设置颜色
            cell.style.background = `rgba(102, 126, 234, ${score})`;
            cell.style.color = score > 0.5 ? 'white' : '#333';

            // 添加点击事件
            cell.addEventListener('click', () => {
                showExampleAttentionDetail(rowWord, colWord, score);
            });

            // 特殊标记高关注度和低关注度的单元格
            if ((rowWord === 'Windows' && (colWord === 'Python' || colWord === '脚本')) ||
                (rowWord === 'Python' && colWord === 'Windows') ||
                (rowWord === '脚本' && colWord === 'Windows')) {
                cell.style.border = '2px solid #ff6b6b';
                cell.style.fontWeight = 'bold';
            }

            if (rowWord === 'Windows' && colWord === '了') {
                cell.style.border = '2px solid #4ecdc4';
                cell.style.fontWeight = 'bold';
            }

            container.appendChild(cell);
        });
    });
}

// 显示示例注意力详情
function showExampleAttentionDetail(fromWord, toWord, score) {
    let explanation = '';

    if (fromWord === 'Windows' && (toWord === 'Python' || toWord === '脚本')) {
        explanation = `🔥 高关注度解析：\n\n"${fromWord}" 对 "${toWord}" 的注意力分数高达 ${score}，这表明：\n\n` +
            `• 语义关联：两者都属于计算机技术领域\n` +
            `• 上下文相关：在编程场景中，Windows、Python、脚本经常一起出现\n` +
            `• 自注意力机制成功识别了这种强关联性\n\n` +
            `这正是Transformer注意力机制的核心价值：自动发现语义相关的词语！`;
    } else if (fromWord === 'Windows' && toWord === '了') {
        explanation = `❄️ 低关注度解析：\n\n"${fromWord}" 对 "${toWord}" 的注意力分数仅为 ${score}，这说明：\n\n` +
            `• 语义距离：技术名词与语气助词语义相距较远\n` +
            `• 功能差异：一个是实体概念，一个是语法功能词\n` +
            `• 智能过滤：模型学会了忽略不相关的词语关系\n\n` +
            `这体现了注意力机制的选择性：重要的关注，不重要的忽略！`;
    } else {
        explanation = `"${fromWord}" 对 "${toWord}" 的注意力分数: ${score}\n\n` +
            `这个分数反映了两个词语在当前上下文中的关联程度。\n\n` +
            `💡 理解要点：\n` +
            `• 分数越高，语义关联越强\n` +
            `• 注意力机制帮助模型理解词语间的关系\n` +
            `• 这是Transformer强大语言理解能力的基础`;
    }

    alert(explanation);
}
