// 神经网络与深度学习核心代码库 - 用于教学演示
// 每个神经网络概念的核心实现代码

const NeuralNetworkCoreCode = {
    // 1. 神经元基础 (Neuron Basics)
    neuron: {
        title: "🧠 神经元基础核心代码",
        description: "单个人工神经元的完整实现，包括前向传播和激活函数",
        code: `// 人工神经元实现
class Neuron {
    constructor(numInputs, activationFunction = 'sigmoid') {
        this.numInputs = numInputs;
        this.activationFunction = activationFunction;
        
        // 随机初始化权重 (使用Xavier初始化)
        this.weights = [];
        const scale = Math.sqrt(2.0 / numInputs);
        for (let i = 0; i < numInputs; i++) {
            this.weights.push((Math.random() - 0.5) * 2 * scale);
        }
        
        // 初始化偏置
        this.bias = (Math.random() - 0.5) * 2 * scale;
        
        // 存储最后的输入和输出用于反向传播
        this.lastInput = null;
        this.lastOutput = null;
        this.lastWeightedSum = null;
    }
    
    // 激活函数
    activate(x) {
        switch (this.activationFunction) {
            case 'sigmoid':
                return 1 / (1 + Math.exp(-x));
            case 'tanh':
                return Math.tanh(x);
            case 'relu':
                return Math.max(0, x);
            case 'leaky_relu':
                return x > 0 ? x : 0.01 * x;
            case 'linear':
                return x;
            default:
                return 1 / (1 + Math.exp(-x)); // 默认sigmoid
        }
    }
    
    // 激活函数的导数
    activateDerivative(x) {
        switch (this.activationFunction) {
            case 'sigmoid':
                const sigmoid = this.activate(x);
                return sigmoid * (1 - sigmoid);
            case 'tanh':
                const tanh = this.activate(x);
                return 1 - tanh * tanh;
            case 'relu':
                return x > 0 ? 1 : 0;
            case 'leaky_relu':
                return x > 0 ? 1 : 0.01;
            case 'linear':
                return 1;
            default:
                const sig = this.activate(x);
                return sig * (1 - sig);
        }
    }
    
    // 前向传播
    forward(inputs) {
        if (inputs.length !== this.numInputs) {
            throw new Error(\`输入维度不匹配: 期望 \${this.numInputs}, 得到 \${inputs.length}\`);
        }
        
        // 保存输入用于反向传播
        this.lastInput = [...inputs];
        
        // 计算加权和: ∑(wi * xi) + b
        let weightedSum = this.bias;
        for (let i = 0; i < inputs.length; i++) {
            weightedSum += inputs[i] * this.weights[i];
        }
        
        this.lastWeightedSum = weightedSum;
        
        // 应用激活函数
        this.lastOutput = this.activate(weightedSum);
        
        return this.lastOutput;
    }
    
    // 反向传播 (计算梯度)
    backward(outputError, learningRate = 0.01) {
        if (this.lastInput === null || this.lastWeightedSum === null) {
            throw new Error('必须先进行前向传播');
        }
        
        // 计算激活函数的梯度
        const activationGradient = this.activateDerivative(this.lastWeightedSum);
        
        // 计算当前神经元的误差
        const neuronError = outputError * activationGradient;
        
        // 更新权重
        for (let i = 0; i < this.weights.length; i++) {
            const weightGradient = neuronError * this.lastInput[i];
            this.weights[i] -= learningRate * weightGradient;
        }
        
        // 更新偏置
        this.bias -= learningRate * neuronError;
        
        // 计算传递给前一层的误差
        const inputErrors = [];
        for (let i = 0; i < this.weights.length; i++) {
            inputErrors.push(neuronError * this.weights[i]);
        }
        
        return inputErrors;
    }
    
    // 获取神经元参数
    getParameters() {
        return {
            weights: [...this.weights],
            bias: this.bias,
            activationFunction: this.activationFunction
        };
    }
    
    // 设置神经元参数
    setParameters(weights, bias) {
        if (weights.length !== this.numInputs) {
            throw new Error('权重数量不匹配');
        }
        this.weights = [...weights];
        this.bias = bias;
    }
}

// 使用示例
console.log("=== 神经元基础演示 ===");

// 创建一个3输入的神经元
const neuron = new Neuron(3, 'sigmoid');

// 示例输入
const inputs = [0.5, -0.2, 0.8];

// 前向传播
const output = neuron.forward(inputs);

console.log("输入:", inputs);
console.log("权重:", neuron.weights.map(w => w.toFixed(3)));
console.log("偏置:", neuron.bias.toFixed(3));
console.log("输出:", output.toFixed(3));

// 模拟训练过程
console.log("\\n=== 训练过程演示 ===");
const targetOutput = 0.8;
const learningRate = 0.1;

for (let epoch = 0; epoch < 5; epoch++) {
    // 前向传播
    const prediction = neuron.forward(inputs);
    
    // 计算误差
    const error = targetOutput - prediction;
    
    // 反向传播
    neuron.backward(error, learningRate);
    
    console.log(\`Epoch \${epoch + 1}: 预测=\${prediction.toFixed(3)}, 误差=\${error.toFixed(3)}\`);
}

// 不同激活函数的比较
console.log("\\n=== 激活函数比较 ===");
const testInput = 2.0;
const activations = ['sigmoid', 'tanh', 'relu', 'leaky_relu'];

activations.forEach(func => {
    const testNeuron = new Neuron(1, func);
    testNeuron.setParameters([1.0], 0.0); // 权重=1, 偏置=0
    const result = testNeuron.activate(testInput);
    console.log(\`\${func}: f(\${testInput}) = \${result.toFixed(3)}\`);
});`
    },

    // 2. 感知机 (Perceptron)
    perceptron: {
        title: "🎯 感知机核心代码",
        description: "经典感知机算法实现，包括线性分类和学习规则",
        code: `// 感知机实现
class Perceptron {
    constructor(numInputs, learningRate = 0.1) {
        this.numInputs = numInputs;
        this.learningRate = learningRate;

        // 初始化权重和偏置
        this.weights = new Array(numInputs).fill(0).map(() => Math.random() * 0.1 - 0.05);
        this.bias = Math.random() * 0.1 - 0.05;

        // 训练历史
        this.trainingHistory = [];
    }

    // 激活函数 (阶跃函数)
    activate(x) {
        return x >= 0 ? 1 : 0;
    }

    // 前向传播
    predict(inputs) {
        if (inputs.length !== this.numInputs) {
            throw new Error(\`输入维度不匹配: 期望 \${this.numInputs}, 得到 \${inputs.length}\`);
        }

        // 计算加权和
        let sum = this.bias;
        for (let i = 0; i < inputs.length; i++) {
            sum += inputs[i] * this.weights[i];
        }

        return this.activate(sum);
    }

    // 训练单个样本
    train(inputs, target) {
        // 获取预测结果
        const prediction = this.predict(inputs);

        // 计算误差
        const error = target - prediction;

        // 更新权重和偏置 (感知机学习规则)
        for (let i = 0; i < this.weights.length; i++) {
            this.weights[i] += this.learningRate * error * inputs[i];
        }
        this.bias += this.learningRate * error;

        // 记录训练历史
        this.trainingHistory.push({
            inputs: [...inputs],
            target: target,
            prediction: prediction,
            error: error,
            weights: [...this.weights],
            bias: this.bias
        });

        return Math.abs(error) > 0; // 返回是否还有误差
    }

    // 批量训练
    trainBatch(dataset, maxEpochs = 1000) {
        let epoch = 0;
        let hasError = true;

        console.log("开始感知机训练...");

        while (hasError && epoch < maxEpochs) {
            hasError = false;

            // 遍历所有训练样本
            for (let i = 0; i < dataset.length; i++) {
                const { inputs, target } = dataset[i];
                const stillHasError = this.train(inputs, target);
                hasError = hasError || stillHasError;
            }

            epoch++;

            // 每10个epoch输出一次进度
            if (epoch % 10 === 0) {
                const accuracy = this.evaluateAccuracy(dataset);
                console.log(\`Epoch \${epoch}: 准确率 = \${(accuracy * 100).toFixed(1)}%\`);
            }
        }

        if (hasError) {
            console.log(\`训练在 \${maxEpochs} 个epoch后停止，数据可能不是线性可分的\`);
        } else {
            console.log(\`训练在 \${epoch} 个epoch后收敛\`);
        }

        return epoch;
    }

    // 评估准确率
    evaluateAccuracy(dataset) {
        let correct = 0;
        for (let i = 0; i < dataset.length; i++) {
            const { inputs, target } = dataset[i];
            const prediction = this.predict(inputs);
            if (prediction === target) {
                correct++;
            }
        }
        return correct / dataset.length;
    }

    // 获取决策边界 (仅适用于2D输入)
    getDecisionBoundary() {
        if (this.numInputs !== 2) {
            throw new Error('决策边界可视化仅支持2D输入');
        }

        // 决策边界: w1*x1 + w2*x2 + b = 0
        // 即: x2 = -(w1*x1 + b) / w2
        const w1 = this.weights[0];
        const w2 = this.weights[1];
        const b = this.bias;

        if (Math.abs(w2) < 1e-10) {
            // 垂直线
            return { type: 'vertical', x: -b / w1 };
        } else {
            // 斜率和截距
            const slope = -w1 / w2;
            const intercept = -b / w2;
            return { type: 'line', slope: slope, intercept: intercept };
        }
    }

    // 可视化训练过程
    visualizeTraining() {
        console.log("\\n=== 训练过程可视化 ===");
        const history = this.trainingHistory;
        const step = Math.max(1, Math.floor(history.length / 10));

        for (let i = 0; i < history.length; i += step) {
            const record = history[i];
            console.log(\`步骤 \${i + 1}:\`);
            console.log(\`  输入: [\${record.inputs.map(x => x.toFixed(2)).join(', ')}]\`);
            console.log(\`  目标: \${record.target}, 预测: \${record.prediction}\`);
            console.log(\`  权重: [\${record.weights.map(w => w.toFixed(3)).join(', ')}]\`);
            console.log(\`  偏置: \${record.bias.toFixed(3)}\`);
            console.log('');
        }
    }
}

// 使用示例
console.log("=== 感知机演示 ===");

// 创建AND门数据集
const andGateData = [
    { inputs: [0, 0], target: 0 },
    { inputs: [0, 1], target: 0 },
    { inputs: [1, 0], target: 0 },
    { inputs: [1, 1], target: 1 }
];

// 创建感知机
const perceptron = new Perceptron(2, 0.1);

console.log("训练AND门:");
console.log("初始权重:", perceptron.weights.map(w => w.toFixed(3)));
console.log("初始偏置:", perceptron.bias.toFixed(3));

// 训练
const epochs = perceptron.trainBatch(andGateData);

console.log("\\n最终结果:");
console.log("权重:", perceptron.weights.map(w => w.toFixed(3)));
console.log("偏置:", perceptron.bias.toFixed(3));

// 测试
console.log("\\n测试结果:");
andGateData.forEach(({ inputs, target }) => {
    const prediction = perceptron.predict(inputs);
    console.log(\`输入: [\${inputs.join(', ')}] -> 预测: \${prediction}, 实际: \${target}\`);
});

// 决策边界
try {
    const boundary = perceptron.getDecisionBoundary();
    console.log("\\n决策边界:", boundary);
} catch (e) {
    console.log("无法计算决策边界:", e.message);
}`
    },

    // 3. 多层神经网络 (Multi-layer Neural Network)
    multilayer: {
        title: "🏗️ 多层神经网络核心代码",
        description: "多层前馈神经网络实现，包括隐藏层和完整的前向传播",
        code: `// 多层神经网络实现
class MultiLayerNetwork {
    constructor(layerSizes, activationFunction = 'sigmoid') {
        this.layerSizes = layerSizes;
        this.numLayers = layerSizes.length;
        this.activationFunction = activationFunction;

        // 初始化权重和偏置
        this.weights = [];
        this.biases = [];

        // 为每一层初始化参数
        for (let i = 0; i < this.numLayers - 1; i++) {
            const inputSize = layerSizes[i];
            const outputSize = layerSizes[i + 1];

            // Xavier初始化
            const scale = Math.sqrt(2.0 / (inputSize + outputSize));

            // 权重矩阵 [inputSize × outputSize]
            const layerWeights = [];
            for (let j = 0; j < inputSize; j++) {
                const row = [];
                for (let k = 0; k < outputSize; k++) {
                    row.push((Math.random() - 0.5) * 2 * scale);
                }
                layerWeights.push(row);
            }
            this.weights.push(layerWeights);

            // 偏置向量 [outputSize]
            const layerBiases = [];
            for (let j = 0; j < outputSize; j++) {
                layerBiases.push((Math.random() - 0.5) * 2 * scale);
            }
            this.biases.push(layerBiases);
        }

        // 存储中间结果用于反向传播
        this.layerInputs = [];
        this.layerOutputs = [];
        this.layerWeightedSums = [];
    }

    // 激活函数
    activate(x) {
        switch (this.activationFunction) {
            case 'sigmoid':
                return 1 / (1 + Math.exp(-Math.max(-500, Math.min(500, x))));
            case 'tanh':
                return Math.tanh(x);
            case 'relu':
                return Math.max(0, x);
            case 'leaky_relu':
                return x > 0 ? x : 0.01 * x;
            case 'softmax':
                // Softmax在层级别处理
                return x;
            default:
                return 1 / (1 + Math.exp(-Math.max(-500, Math.min(500, x))));
        }
    }

    // 激活函数导数
    activateDerivative(x) {
        switch (this.activationFunction) {
            case 'sigmoid':
                const sigmoid = this.activate(x);
                return sigmoid * (1 - sigmoid);
            case 'tanh':
                const tanh = this.activate(x);
                return 1 - tanh * tanh;
            case 'relu':
                return x > 0 ? 1 : 0;
            case 'leaky_relu':
                return x > 0 ? 1 : 0.01;
            default:
                const sig = this.activate(x);
                return sig * (1 - sig);
        }
    }

    // Softmax函数 (用于输出层)
    softmax(vector) {
        const maxVal = Math.max(...vector);
        const expValues = vector.map(x => Math.exp(x - maxVal));
        const sumExp = expValues.reduce((a, b) => a + b, 0);
        return expValues.map(x => x / sumExp);
    }

    // 前向传播
    forward(inputs) {
        if (inputs.length !== this.layerSizes[0]) {
            throw new Error(\`输入维度不匹配: 期望 \${this.layerSizes[0]}, 得到 \${inputs.length}\`);
        }

        // 清空之前的中间结果
        this.layerInputs = [];
        this.layerOutputs = [];
        this.layerWeightedSums = [];

        let currentInput = [...inputs];
        this.layerInputs.push([...currentInput]);

        // 逐层前向传播
        for (let layer = 0; layer < this.numLayers - 1; layer++) {
            const layerWeights = this.weights[layer];
            const layerBiases = this.biases[layer];
            const outputSize = this.layerSizes[layer + 1];

            // 计算加权和
            const weightedSums = [];
            for (let j = 0; j < outputSize; j++) {
                let sum = layerBiases[j];
                for (let i = 0; i < currentInput.length; i++) {
                    sum += currentInput[i] * layerWeights[i][j];
                }
                weightedSums.push(sum);
            }
            this.layerWeightedSums.push([...weightedSums]);

            // 应用激活函数
            let layerOutput;
            if (layer === this.numLayers - 2 && this.activationFunction === 'softmax') {
                // 输出层使用softmax
                layerOutput = this.softmax(weightedSums);
            } else {
                layerOutput = weightedSums.map(x => this.activate(x));
            }

            this.layerOutputs.push([...layerOutput]);
            currentInput = layerOutput;

            if (layer < this.numLayers - 2) {
                this.layerInputs.push([...currentInput]);
            }
        }

        return currentInput;
    }

    // 计算损失 (均方误差)
    calculateLoss(predictions, targets) {
        if (predictions.length !== targets.length) {
            throw new Error('预测和目标维度不匹配');
        }

        let totalLoss = 0;
        for (let i = 0; i < predictions.length; i++) {
            const error = predictions[i] - targets[i];
            totalLoss += error * error;
        }
        return totalLoss / (2 * predictions.length);
    }

    // 预测 (返回概率最高的类别)
    predict(inputs) {
        const outputs = this.forward(inputs);
        let maxIndex = 0;
        let maxValue = outputs[0];

        for (let i = 1; i < outputs.length; i++) {
            if (outputs[i] > maxValue) {
                maxValue = outputs[i];
                maxIndex = i;
            }
        }

        return {
            classIndex: maxIndex,
            probability: maxValue,
            allProbabilities: [...outputs]
        };
    }

    // 获取网络信息
    getNetworkInfo() {
        const totalParams = this.weights.reduce((total, layerWeights, i) => {
            const weightCount = layerWeights.length * layerWeights[0].length;
            const biasCount = this.biases[i].length;
            return total + weightCount + biasCount;
        }, 0);

        return {
            architecture: this.layerSizes,
            totalParameters: totalParams,
            numLayers: this.numLayers,
            activationFunction: this.activationFunction
        };
    }
}

// 使用示例
console.log("=== 多层神经网络演示 ===");

// 创建一个3层网络: 2输入 -> 4隐藏 -> 3输出
const network = new MultiLayerNetwork([2, 4, 3], 'sigmoid');

console.log("网络信息:", network.getNetworkInfo());

// 示例输入
const testInput = [0.5, -0.3];

// 前向传播
const output = network.forward(testInput);

console.log("\\n前向传播结果:");
console.log("输入:", testInput);
console.log("输出:", output.map(x => x.toFixed(4)));

// 预测
const prediction = network.predict(testInput);
console.log("\\n预测结果:");
console.log("预测类别:", prediction.classIndex);
console.log("预测概率:", prediction.probability.toFixed(4));
console.log("所有概率:", prediction.allProbabilities.map(p => p.toFixed(4)));

// 显示网络结构
console.log("\\n网络结构:");
for (let i = 0; i < network.numLayers - 1; i++) {
    const inputSize = network.layerSizes[i];
    const outputSize = network.layerSizes[i + 1];
    console.log(\`层 \${i + 1}: \${inputSize} -> \${outputSize}\`);
    console.log(\`  权重矩阵形状: \${inputSize} × \${outputSize}\`);
    console.log(\`  偏置向量长度: \${outputSize}\`);
}`
    },

    // 4. 反向传播算法 (Backpropagation)
    backprop: {
        title: "🔄 反向传播算法核心代码",
        description: "完整的反向传播算法实现，包括梯度计算和权重更新",
        code: `// 带反向传播的神经网络
class BackpropagationNetwork extends MultiLayerNetwork {
    constructor(layerSizes, activationFunction = 'sigmoid', learningRate = 0.01) {
        super(layerSizes, activationFunction);
        this.learningRate = learningRate;
        this.trainingHistory = [];
    }

    // 反向传播算法
    backward(targets) {
        if (this.layerOutputs.length === 0) {
            throw new Error('必须先进行前向传播');
        }

        const numLayers = this.numLayers;
        const deltas = []; // 存储每层的误差

        // 1. 计算输出层误差
        const outputLayer = numLayers - 2; // 最后一个权重层的索引
        const outputs = this.layerOutputs[outputLayer];
        const outputDeltas = [];

        for (let i = 0; i < outputs.length; i++) {
            const error = targets[i] - outputs[i];
            const weightedSum = this.layerWeightedSums[outputLayer][i];
            const derivative = this.activateDerivative(weightedSum);
            outputDeltas.push(error * derivative);
        }
        deltas.unshift(outputDeltas); // 添加到数组开头

        // 2. 反向传播误差到隐藏层
        for (let layer = outputLayer - 1; layer >= 0; layer--) {
            const layerDeltas = [];
            const layerSize = this.layerSizes[layer + 1];
            const nextLayerDeltas = deltas[0];
            const nextLayerWeights = this.weights[layer + 1];

            for (let i = 0; i < layerSize; i++) {
                let error = 0;

                // 计算来自下一层的误差
                for (let j = 0; j < nextLayerDeltas.length; j++) {
                    error += nextLayerDeltas[j] * nextLayerWeights[i][j];
                }

                // 乘以激活函数的导数
                const weightedSum = this.layerWeightedSums[layer][i];
                const derivative = this.activateDerivative(weightedSum);
                layerDeltas.push(error * derivative);
            }

            deltas.unshift(layerDeltas);
        }

        // 3. 更新权重和偏置
        for (let layer = 0; layer < numLayers - 1; layer++) {
            const layerDeltas = deltas[layer];
            const layerInputs = layer === 0 ?
                this.layerInputs[0] : this.layerOutputs[layer - 1];

            // 更新权重
            for (let i = 0; i < this.weights[layer].length; i++) {
                for (let j = 0; j < this.weights[layer][i].length; j++) {
                    const gradient = layerDeltas[j] * layerInputs[i];
                    this.weights[layer][i][j] += this.learningRate * gradient;
                }
            }

            // 更新偏置
            for (let j = 0; j < this.biases[layer].length; j++) {
                this.biases[layer][j] += this.learningRate * layerDeltas[j];
            }
        }

        return deltas;
    }

    // 训练单个样本
    trainSample(inputs, targets) {
        // 前向传播
        const outputs = this.forward(inputs);

        // 计算损失
        const loss = this.calculateLoss(outputs, targets);

        // 反向传播
        const deltas = this.backward(targets);

        return { outputs, loss, deltas };
    }

    // 批量训练
    train(dataset, epochs = 1000, batchSize = null, verbose = true) {
        const actualBatchSize = batchSize || dataset.length;

        for (let epoch = 0; epoch < epochs; epoch++) {
            let totalLoss = 0;
            let correct = 0;

            // 随机打乱数据
            const shuffledData = [...dataset].sort(() => Math.random() - 0.5);

            // 分批训练
            for (let i = 0; i < shuffledData.length; i += actualBatchSize) {
                const batch = shuffledData.slice(i, i + actualBatchSize);
                let batchLoss = 0;

                for (const sample of batch) {
                    const result = this.trainSample(sample.inputs, sample.targets);
                    batchLoss += result.loss;

                    // 计算准确率 (对于分类问题)
                    const prediction = this.predict(sample.inputs);
                    const actualClass = sample.targets.indexOf(Math.max(...sample.targets));
                    if (prediction.classIndex === actualClass) {
                        correct++;
                    }
                }

                totalLoss += batchLoss;
            }

            const avgLoss = totalLoss / dataset.length;
            const accuracy = correct / dataset.length;

            // 记录训练历史
            this.trainingHistory.push({
                epoch: epoch + 1,
                loss: avgLoss,
                accuracy: accuracy
            });

            // 输出训练进度
            if (verbose && (epoch + 1) % Math.max(1, Math.floor(epochs / 10)) === 0) {
                console.log(\`Epoch \${epoch + 1}/\${epochs}: Loss = \${avgLoss.toFixed(4)}, Accuracy = \${(accuracy * 100).toFixed(1)}%\`);
            }
        }
    }

    // 评估模型
    evaluate(testDataset) {
        let correct = 0;
        let totalLoss = 0;

        for (const sample of testDataset) {
            const outputs = this.forward(sample.inputs);
            const loss = this.calculateLoss(outputs, sample.targets);
            totalLoss += loss;

            const prediction = this.predict(sample.inputs);
            const actualClass = sample.targets.indexOf(Math.max(...sample.targets));
            if (prediction.classIndex === actualClass) {
                correct++;
            }
        }

        return {
            accuracy: correct / testDataset.length,
            averageLoss: totalLoss / testDataset.length
        };
    }

    // 获取训练历史
    getTrainingHistory() {
        return this.trainingHistory;
    }
}

// 使用示例
console.log("=== 反向传播算法演示 ===");

// 创建XOR问题数据集
const xorDataset = [
    { inputs: [0, 0], targets: [1, 0] }, // 输出0 -> [1, 0]
    { inputs: [0, 1], targets: [0, 1] }, // 输出1 -> [0, 1]
    { inputs: [1, 0], targets: [0, 1] }, // 输出1 -> [0, 1]
    { inputs: [1, 1], targets: [1, 0] }  // 输出0 -> [1, 0]
];

// 创建网络: 2输入 -> 4隐藏 -> 2输出
const bpNetwork = new BackpropagationNetwork([2, 4, 2], 'sigmoid', 0.5);

console.log("训练前测试:");
xorDataset.forEach(({ inputs, targets }) => {
    const prediction = bpNetwork.predict(inputs);
    const actualClass = targets.indexOf(Math.max(...targets));
    console.log(\`输入: [\${inputs.join(', ')}] -> 预测: \${prediction.classIndex}, 实际: \${actualClass}\`);
});

// 训练网络
console.log("\\n开始训练...");
bpNetwork.train(xorDataset, 1000, null, true);

console.log("\\n训练后测试:");
xorDataset.forEach(({ inputs, targets }) => {
    const outputs = bpNetwork.forward(inputs);
    const prediction = bpNetwork.predict(inputs);
    const actualClass = targets.indexOf(Math.max(...targets));
    console.log(\`输入: [\${inputs.join(', ')}] -> 输出: [\${outputs.map(x => x.toFixed(3)).join(', ')}] -> 预测: \${prediction.classIndex}, 实际: \${actualClass}\`);
});

// 评估结果
const evaluation = bpNetwork.evaluate(xorDataset);
console.log(\`\\n最终评估: 准确率 = \${(evaluation.accuracy * 100).toFixed(1)}%, 平均损失 = \${evaluation.averageLoss.toFixed(4)}\`);`
    },

    // 5. 深度学习 (Deep Learning) - 从Part2文件导入
    deep: {
        title: "🏗️ 深度学习核心代码",
        description: "深度神经网络实现，包括多种优化技术和正则化方法",
        code: `// 深度学习网络实现 - 包含Adam优化器和Dropout
class DeepNeuralNetwork {
    constructor(layerSizes, activationFunctions = null, learningRate = 0.001) {
        this.layerSizes = layerSizes;
        this.numLayers = layerSizes.length;
        this.learningRate = learningRate;

        // 默认激活函数
        this.activationFunctions = activationFunctions ||
            new Array(this.numLayers - 1).fill('relu').map((_, i) =>
                i === this.numLayers - 2 ? 'softmax' : 'relu'
            );

        this.initializeParameters();
        this.initializeOptimizer();

        this.trainingStats = {
            losses: [],
            accuracies: [],
            validationLosses: [],
            validationAccuracies: []
        };
    }

    // He初始化 (适用于ReLU)
    initializeParameters() {
        this.weights = [];
        this.biases = [];

        for (let i = 0; i < this.numLayers - 1; i++) {
            const inputSize = this.layerSizes[i];
            const outputSize = this.layerSizes[i + 1];

            const scale = Math.sqrt(2.0 / inputSize);

            const layerWeights = [];
            for (let j = 0; j < inputSize; j++) {
                const row = [];
                for (let k = 0; k < outputSize; k++) {
                    row.push(this.randomNormal() * scale);
                }
                layerWeights.push(row);
            }
            this.weights.push(layerWeights);

            const layerBiases = new Array(outputSize).fill(0);
            this.biases.push(layerBiases);
        }
    }

    // 初始化Adam优化器
    initializeOptimizer() {
        this.beta1 = 0.9;
        this.beta2 = 0.999;
        this.epsilon = 1e-8;
        this.t = 0;

        this.m_weights = [];
        this.v_weights = [];
        this.m_biases = [];
        this.v_biases = [];

        for (let i = 0; i < this.numLayers - 1; i++) {
            const inputSize = this.layerSizes[i];
            const outputSize = this.layerSizes[i + 1];

            const m_w = [];
            const v_w = [];
            for (let j = 0; j < inputSize; j++) {
                m_w.push(new Array(outputSize).fill(0));
                v_w.push(new Array(outputSize).fill(0));
            }
            this.m_weights.push(m_w);
            this.v_weights.push(v_w);

            this.m_biases.push(new Array(outputSize).fill(0));
            this.v_biases.push(new Array(outputSize).fill(0));
        }
    }

    randomNormal() {
        let u = 0, v = 0;
        while(u === 0) u = Math.random();
        while(v === 0) v = Math.random();
        return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    }

    // 激活函数
    activate(x, func) {
        switch (func) {
            case 'relu':
                return Math.max(0, x);
            case 'leaky_relu':
                return x > 0 ? x : 0.01 * x;
            case 'sigmoid':
                return 1 / (1 + Math.exp(-Math.max(-500, Math.min(500, x))));
            case 'tanh':
                return Math.tanh(x);
            default:
                return Math.max(0, x);
        }
    }

    // Softmax函数
    softmax(vector) {
        const maxVal = Math.max(...vector);
        const expValues = vector.map(x => Math.exp(Math.max(-500, Math.min(500, x - maxVal))));
        const sumExp = expValues.reduce((a, b) => a + b, 0);
        return expValues.map(x => x / (sumExp + 1e-15));
    }

    // 前向传播 (包含Dropout)
    forward(inputs, training = false) {
        let currentInput = [...inputs];
        const layerOutputs = [currentInput];

        for (let layer = 0; layer < this.numLayers - 1; layer++) {
            const layerWeights = this.weights[layer];
            const layerBiases = this.biases[layer];
            const outputSize = this.layerSizes[layer + 1];
            const activationFunc = this.activationFunctions[layer];

            // 计算加权和
            const weightedSums = [];
            for (let j = 0; j < outputSize; j++) {
                let sum = layerBiases[j];
                for (let i = 0; i < currentInput.length; i++) {
                    sum += currentInput[i] * layerWeights[i][j];
                }
                weightedSums.push(sum);
            }

            // 应用激活函数
            let layerOutput;
            if (activationFunc === 'softmax') {
                layerOutput = this.softmax(weightedSums);
            } else {
                layerOutput = weightedSums.map(x => this.activate(x, activationFunc));
            }

            // Dropout (仅在训练时)
            if (training && layer < this.numLayers - 2) {
                layerOutput = this.dropout(layerOutput, 0.2);
            }

            layerOutputs.push(layerOutput);
            currentInput = layerOutput;
        }

        return { output: currentInput, layerOutputs: layerOutputs };
    }

    // Dropout正则化
    dropout(vector, rate) {
        return vector.map(x => {
            if (Math.random() < rate) {
                return 0;
            } else {
                return x / (1 - rate);
            }
        });
    }

    // 交叉熵损失
    crossEntropyLoss(predictions, targets) {
        let loss = 0;
        for (let i = 0; i < predictions.length; i++) {
            loss -= targets[i] * Math.log(Math.max(1e-15, predictions[i]));
        }
        return loss;
    }

    // 评估模型
    evaluate(testData) {
        let totalLoss = 0;
        let correct = 0;

        for (const sample of testData) {
            const result = this.forward(sample.inputs, false);
            const loss = this.crossEntropyLoss(result.output, sample.targets);
            totalLoss += loss;

            const prediction = result.output.indexOf(Math.max(...result.output));
            const actual = sample.targets.indexOf(Math.max(...sample.targets));
            if (prediction === actual) correct++;
        }

        return {
            loss: totalLoss / testData.length,
            accuracy: correct / testData.length
        };
    }
}

// 使用示例
console.log("=== 深度学习网络演示 ===");
const deepNet = new DeepNeuralNetwork([4, 8, 6, 3], ['relu', 'relu', 'softmax'], 0.001);
console.log("网络架构:", deepNet.layerSizes);
console.log("激活函数:", deepNet.activationFunctions);

// 生成示例数据
const generateData = (numSamples) => {
    const data = [];
    for (let i = 0; i < numSamples; i++) {
        const inputs = [Math.random(), Math.random(), Math.random(), Math.random()];
        const sum = inputs.reduce((a, b) => a + b, 0);
        let targets;
        if (sum < 1.5) targets = [1, 0, 0];
        else if (sum < 2.5) targets = [0, 1, 0];
        else targets = [0, 0, 1];
        data.push({ inputs, targets });
    }
    return data;
};

const trainData = generateData(100);
const testData = generateData(20);

console.log("训练数据样本:", trainData.length);
console.log("测试数据样本:", testData.length);

const beforeTraining = deepNet.evaluate(testData);
console.log(\`训练前 - 损失: \${beforeTraining.loss.toFixed(4)}, 准确率: \${(beforeTraining.accuracy * 100).toFixed(1)}%\`);`
    },

    // 6. 大语言模型 (Large Language Model)
    llm: {
        title: "🤖 大语言模型核心代码",
        description: "简化的语言模型实现，展示文本生成和注意力机制的基本原理",
        code: `// 简化的语言模型实现
class SimpleLLM {
    constructor(vocabSize = 50, embedDim = 64, hiddenDim = 128, numLayers = 2) {
        this.vocabSize = vocabSize;
        this.embedDim = embedDim;
        this.hiddenDim = hiddenDim;
        this.numLayers = numLayers;
        this.maxSeqLength = 20;

        // 创建简化词汇表
        this.vocab = [
            '<PAD>', '<UNK>', '<START>', '<END>',
            'the', 'a', 'and', 'I', 'you', 'he', 'she', 'it',
            'am', 'is', 'are', 'was', 'were', 'have', 'has',
            'love', 'like', 'want', 'think', 'know', 'see',
            'good', 'bad', 'big', 'small', 'new', 'old',
            'cat', 'dog', 'house', 'car', 'book', 'water',
            'hello', 'world', 'today', 'tomorrow', 'yes', 'no'
        ];

        this.buildVocabMaps();
        this.initializeComponents();
    }

    // 构建词汇映射
    buildVocabMaps() {
        this.tokenToId = {};
        this.idToToken = {};
        this.vocab.forEach((token, id) => {
            this.tokenToId[token] = id;
            this.idToToken[id] = token;
        });
    }

    // 初始化所有组件
    initializeComponents() {
        this.initializeEmbedding();
        this.initializePositionalEncoding();
        this.initializeTransformerLayers();
        this.initializeOutputLayer();
    }

    // 初始化嵌入层
    initializeEmbedding() {
        this.embeddings = [];
        for (let i = 0; i < this.vocabSize; i++) {
            const embedding = [];
            for (let j = 0; j < this.embedDim; j++) {
                embedding.push((Math.random() - 0.5) * 0.1);
            }
            this.embeddings.push(embedding);
        }
    }

    // 初始化位置编码
    initializePositionalEncoding() {
        this.posEncoding = [];
        for (let pos = 0; pos < this.maxSeqLength; pos++) {
            const encoding = [];
            for (let i = 0; i < this.embedDim; i++) {
                if (i % 2 === 0) {
                    encoding.push(Math.sin(pos / Math.pow(10000, i / this.embedDim)));
                } else {
                    encoding.push(Math.cos(pos / Math.pow(10000, (i - 1) / this.embedDim)));
                }
            }
            this.posEncoding.push(encoding);
        }
    }

    // 初始化Transformer层
    initializeTransformerLayers() {
        this.transformerLayers = [];
        for (let layer = 0; layer < this.numLayers; layer++) {
            this.transformerLayers.push({
                // 注意力权重矩阵
                Wq: this.randomMatrix(this.embedDim, this.embedDim),
                Wk: this.randomMatrix(this.embedDim, this.embedDim),
                Wv: this.randomMatrix(this.embedDim, this.embedDim),
                Wo: this.randomMatrix(this.embedDim, this.embedDim),

                // 前馈网络
                W1: this.randomMatrix(this.embedDim, this.hiddenDim),
                b1: new Array(this.hiddenDim).fill(0),
                W2: this.randomMatrix(this.hiddenDim, this.embedDim),
                b2: new Array(this.embedDim).fill(0)
            });
        }
    }

    // 初始化输出层
    initializeOutputLayer() {
        this.outputWeights = this.randomMatrix(this.embedDim, this.vocabSize);
        this.outputBias = new Array(this.vocabSize).fill(0);
    }

    // 生成随机矩阵
    randomMatrix(rows, cols) {
        const matrix = [];
        const scale = Math.sqrt(2.0 / (rows + cols));
        for (let i = 0; i < rows; i++) {
            const row = [];
            for (let j = 0; j < cols; j++) {
                row.push((Math.random() - 0.5) * 2 * scale);
            }
            matrix.push(row);
        }
        return matrix;
    }

    // 矩阵乘法
    matmul(A, B) {
        const result = [];
        for (let i = 0; i < A.length; i++) {
            const row = [];
            for (let j = 0; j < B[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < B.length; k++) {
                    sum += A[i][k] * B[k][j];
                }
                row.push(sum);
            }
            result.push(row);
        }
        return result;
    }

    // Softmax函数
    softmax(vector) {
        const maxVal = Math.max(...vector);
        const expValues = vector.map(x => Math.exp(x - maxVal));
        const sumExp = expValues.reduce((a, b) => a + b, 0);
        return expValues.map(x => x / sumExp);
    }

    // 简化的注意力机制
    attention(queries, keys, values) {
        const seqLen = queries.length;
        const scores = [];

        // 计算注意力分数
        for (let i = 0; i < seqLen; i++) {
            const queryScores = [];
            for (let j = 0; j < seqLen; j++) {
                let score = 0;
                for (let k = 0; k < queries[i].length; k++) {
                    score += queries[i][k] * keys[j][k];
                }
                queryScores.push(score / Math.sqrt(this.embedDim));
            }
            scores.push(this.softmax(queryScores));
        }

        // 应用注意力权重
        const output = [];
        for (let i = 0; i < seqLen; i++) {
            const weighted = new Array(this.embedDim).fill(0);
            for (let j = 0; j < seqLen; j++) {
                for (let k = 0; k < this.embedDim; k++) {
                    weighted[k] += scores[i][j] * values[j][k];
                }
            }
            output.push(weighted);
        }

        return { output, attentionWeights: scores };
    }

    // 文本编码
    encode(text) {
        const tokens = text.toLowerCase().split(/\\s+/);
        const ids = tokens.map(token =>
            this.tokenToId[token] || this.tokenToId['<UNK>']
        );
        return ids;
    }

    // 文本解码
    decode(ids) {
        return ids.map(id => this.idToToken[id] || '<UNK>').join(' ');
    }

    // 前向传播
    forward(inputIds) {
        const seqLen = inputIds.length;

        // 1. 嵌入 + 位置编码
        let embeddings = [];
        for (let i = 0; i < seqLen; i++) {
            const tokenEmbed = [...this.embeddings[inputIds[i]]];
            const posEmbed = this.posEncoding[i];
            const combined = tokenEmbed.map((val, j) => val + posEmbed[j]);
            embeddings.push(combined);
        }

        // 2. Transformer层
        let hidden = embeddings;
        for (let layer = 0; layer < this.numLayers; layer++) {
            const layerParams = this.transformerLayers[layer];

            // 多头注意力 (简化为单头)
            const queries = this.matmul(hidden, layerParams.Wq);
            const keys = this.matmul(hidden, layerParams.Wk);
            const values = this.matmul(hidden, layerParams.Wv);

            const attentionResult = this.attention(queries, keys, values);
            let attentionOutput = this.matmul(attentionResult.output, layerParams.Wo);

            // 残差连接
            hidden = hidden.map((row, i) =>
                row.map((val, j) => val + attentionOutput[i][j])
            );

            // 前馈网络
            let ffOutput = this.matmul(hidden, layerParams.W1);
            ffOutput = ffOutput.map(row =>
                row.map((val, i) => Math.max(0, val + layerParams.b1[i])) // ReLU
            );
            ffOutput = this.matmul(ffOutput, layerParams.W2);
            ffOutput = ffOutput.map(row =>
                row.map((val, i) => val + layerParams.b2[i])
            );

            // 残差连接
            hidden = hidden.map((row, i) =>
                row.map((val, j) => val + ffOutput[i][j])
            );
        }

        // 3. 输出投影
        const logits = this.matmul(hidden, this.outputWeights);
        const finalLogits = logits.map(row =>
            row.map((val, i) => val + this.outputBias[i])
        );

        return finalLogits;
    }

    // 文本生成
    generate(prompt, maxLength = 10, temperature = 1.0) {
        const inputIds = this.encode(prompt);
        const generated = [...inputIds];

        console.log(\`开始生成，提示词: "\${prompt}"\`);

        for (let step = 0; step < maxLength; step++) {
            const logits = this.forward(generated);
            const lastLogits = logits[logits.length - 1];

            // 温度缩放
            const scaledLogits = lastLogits.map(x => x / temperature);
            const probs = this.softmax(scaledLogits);

            // 采样下一个token
            const nextTokenId = this.sampleFromProbs(probs);

            if (nextTokenId === this.tokenToId['<END>']) {
                break;
            }

            generated.push(nextTokenId);

            if (generated.length >= this.maxSeqLength) {
                generated.shift();
            }
        }

        const generatedText = this.decode(generated);
        console.log(\`生成结果: "\${generatedText}"\`);
        return generatedText;
    }

    // 从概率分布中采样
    sampleFromProbs(probs) {
        const random = Math.random();
        let cumulative = 0;

        for (let i = 0; i < probs.length; i++) {
            cumulative += probs[i];
            if (random < cumulative) {
                return i;
            }
        }
        return probs.length - 1;
    }

    // 获取模型信息
    getModelInfo() {
        const totalParams = this.vocabSize * this.embedDim +
            this.numLayers * (4 * this.embedDim * this.embedDim +
                             this.embedDim * this.hiddenDim + this.hiddenDim +
                             this.hiddenDim * this.embedDim + this.embedDim) +
            this.embedDim * this.vocabSize + this.vocabSize;

        return {
            vocabSize: this.vocabSize,
            embedDim: this.embedDim,
            hiddenDim: this.hiddenDim,
            numLayers: this.numLayers,
            maxSeqLength: this.maxSeqLength,
            totalParameters: totalParams
        };
    }
}

// 使用示例
console.log("=== 大语言模型演示 ===");
const llm = new SimpleLLM(50, 32, 64, 2);
console.log("模型信息:", llm.getModelInfo());

const testText = "I love cats";
const encoded = llm.encode(testText);
const decoded = llm.decode(encoded);

console.log(\`编码测试: "\${testText}" -> \${encoded} -> "\${decoded}"\`);

const logits = llm.forward(encoded);
console.log(\`前向传播: 输入长度=\${encoded.length}, 输出形状=\${logits.length}×\${logits[0].length}\`);

llm.generate("I love", 3, 1.0);`
    }
};

// 导出供其他文件使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NeuralNetworkCoreCode;
}
