// MCP三层架构演示 - 主要逻辑

class MCPSystem {
    constructor() {
        this.host = new HostLayer();
        this.client = new ClientLayer();
        this.server = new ServerLayer();
        this.requestCount = 0;
        this.responseTimes = [];
        this.successCount = 0;
        this.initializeSystem();
        this.setupEventListeners();
    }

    initializeSystem() {
        console.log('MCP系统初始化...');
        this.updateMonitoringStats();
    }

    setupEventListeners() {
        // 请求类型选择事件
        document.getElementById('requestType').addEventListener('change', (e) => {
            this.host.selectRequestType(e.target.value);
        });

        // 请求内容输入事件
        document.getElementById('requestContent').addEventListener('input', (e) => {
            this.host.updateSendButton();
        });

        // 发送请求事件
        document.getElementById('sendRequest').addEventListener('click', () => {
            this.processRequest();
        });
    }

    async processRequest() {
        const startTime = Date.now();
        this.requestCount++;

        try {
            // 重置所有状态
            this.resetSystemState();

            // 获取请求信息
            const requestData = this.host.getRequestData();
            if (!requestData.type || !requestData.content.trim()) {
                alert('请完整填写请求信息');
                return;
            }

            // 开始MCP四步流程
            await this.executeStep1_HostRequest(requestData);
            await this.executeStep2_ClientProcess(requestData);
            await this.executeStep3_ServerExecute(requestData);
            await this.executeStep4_ResponseReturn(requestData);

            // 记录成功
            this.successCount++;
            const responseTime = Date.now() - startTime;
            this.responseTimes.push(responseTime);
            this.updateMonitoringStats();

        } catch (error) {
            console.error('请求处理失败:', error);
            this.host.showError('请求处理失败，请重试');
        }
    }

    resetSystemState() {
        // 重置层级状态
        document.querySelectorAll('.layer').forEach(layer => {
            layer.classList.remove('active');
        });

        // 重置流程步骤
        document.querySelectorAll('.flow-step').forEach(step => {
            step.classList.remove('active');
        });

        // 重置箭头
        document.querySelectorAll('.arrow-container, .return-arrow').forEach(arrow => {
            arrow.classList.remove('active');
        });

        // 隐藏消息气泡
        document.querySelectorAll('.message-bubble').forEach(bubble => {
            bubble.classList.remove('show');
            bubble.textContent = '';
        });

        // 重置Client状态指示器
        document.getElementById('authStatus').textContent = '未验证';
        document.getElementById('protocolStatus').textContent = '待转换';
        document.getElementById('routingStatus').textContent = '待路由';
        document.querySelectorAll('.status-indicator').forEach(indicator => {
            indicator.classList.remove('active', 'processing');
        });

        // 重置Server资源状态
        document.querySelectorAll('.resource-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    async executeStep1_HostRequest(requestData) {
        // 激活步骤1和Host层
        document.getElementById('step1').classList.add('active');
        document.getElementById('hostLayer').classList.add('active');
        document.getElementById('hostStatus').textContent = '发送请求中';

        this.host.showStatus('正在发送请求到Client层...');

        await this.delay(1500);

        // 显示Host到Client的通信
        const hostToClient = document.getElementById('hostToClient');
        const hostMessage = document.getElementById('hostMessage');

        hostToClient.classList.add('active');
        hostMessage.textContent = `${this.getRequestTypeText(requestData.type)}请求`;
        hostMessage.classList.add('show');

        await this.delay(1000);
    }

    async executeStep2_ClientProcess(requestData) {
        // 激活步骤2和Client层
        document.getElementById('step2').classList.add('active');
        document.getElementById('clientLayer').classList.add('active');
        document.getElementById('clientStatus').textContent = '处理中';

        // 安全验证
        document.getElementById('authStatus').textContent = '验证中...';
        document.getElementById('authStatus').classList.add('processing');
        this.client.addLog('开始用户身份验证...');

        await this.delay(800);

        document.getElementById('authStatus').textContent = '已验证';
        document.getElementById('authStatus').classList.remove('processing');
        document.getElementById('authStatus').classList.add('active');
        this.client.addLog('用户身份验证通过 ✓');

        // 协议转换
        document.getElementById('protocolStatus').textContent = '转换中...';
        document.getElementById('protocolStatus').classList.add('processing');
        this.client.addLog('将HTTP请求转换为JSON-RPC格式...');

        await this.delay(800);

        document.getElementById('protocolStatus').textContent = '已转换';
        document.getElementById('protocolStatus').classList.remove('processing');
        document.getElementById('protocolStatus').classList.add('active');
        this.client.addLog('协议转换完成 ✓');

        // 请求路由
        document.getElementById('routingStatus').textContent = '路由中...';
        document.getElementById('routingStatus').classList.add('processing');
        this.client.addLog(`路由到${this.getRequestTypeText(requestData.type)}服务...`);

        await this.delay(800);

        document.getElementById('routingStatus').textContent = '已路由';
        document.getElementById('routingStatus').classList.remove('processing');
        document.getElementById('routingStatus').classList.add('active');
        this.client.addLog('请求路由完成 ✓');

        // 显示Client到Server的通信
        const clientToServer = document.getElementById('clientToServer');
        const clientMessage = document.getElementById('clientMessage');

        clientToServer.classList.add('active');
        clientMessage.textContent = `JSON-RPC: ${requestData.type}()`;
        clientMessage.classList.add('show');

        await this.delay(1000);
    }

    async executeStep3_ServerExecute(requestData) {
        // 激活步骤3和Server层
        document.getElementById('step3').classList.add('active');
        document.getElementById('serverLayer').classList.add('active');
        document.getElementById('serverStatus').textContent = '执行中';

        // 激活对应的服务资源
        const serviceElement = document.querySelector(`[data-service="${requestData.type}"]`);
        if (serviceElement) {
            serviceElement.classList.add('active');
        }

        this.server.addLog(`接收到${this.getRequestTypeText(requestData.type)}请求`);
        this.server.addLog('检查服务可用性...');

        await this.delay(1000);

        this.server.addLog('服务可用，开始处理请求');
        this.server.addLog(`执行${this.getRequestTypeText(requestData.type)}逻辑...`);

        await this.delay(1500);

        // 模拟具体的服务处理
        const result = await this.server.processService(requestData.type, requestData.content);
        this.server.addLog(`处理完成，生成结果: ${result.substring(0, 30)}...`);

        await this.delay(500);
    }

    async executeStep4_ResponseReturn(requestData) {
        // 激活步骤4
        document.getElementById('step4').classList.add('active');

        // Server返回响应
        const serverToClient = document.getElementById('serverToClient');
        const serverMessage = document.getElementById('serverMessage');

        serverToClient.classList.add('active');
        serverMessage.textContent = '处理结果返回';
        serverMessage.classList.add('show');

        this.server.addLog('响应已发送到Client层');

        await this.delay(1500);

        // Client处理响应并返回Host
        this.client.addLog('接收到Server响应');
        this.client.addLog('验证响应格式...');

        await this.delay(800);

        this.client.addLog('转换响应格式为用户友好格式');

        const clientToHost = document.getElementById('clientToHost');
        const returnMessage = document.getElementById('returnMessage');

        clientToHost.classList.add('active');
        returnMessage.textContent = '最终结果';
        returnMessage.classList.add('show');

        this.client.addLog('响应已发送到Host层');

        await this.delay(1500);

        // Host显示最终结果
        document.getElementById('hostStatus').textContent = '请求完成';
        const result = await this.server.processService(requestData.type, requestData.content);
        this.host.showSuccess(requestData, result);

        // 重置系统状态
        setTimeout(() => {
            this.resetToIdleState();
        }, 3000);
    }

    resetToIdleState() {
        // 重置所有状态到待机
        document.querySelectorAll('.layer').forEach(layer => {
            layer.classList.remove('active');
        });

        document.querySelectorAll('.flow-step').forEach(step => {
            step.classList.remove('active');
        });

        document.querySelectorAll('.arrow-container, .return-arrow').forEach(arrow => {
            arrow.classList.remove('active');
        });

        document.querySelectorAll('.message-bubble').forEach(bubble => {
            bubble.classList.remove('show');
        });

        document.getElementById('hostStatus').textContent = '待机中';
        document.getElementById('clientStatus').textContent = '就绪';
        document.getElementById('serverStatus').textContent = '运行中';

        this.host.showStatus('等待用户发送请求...');
    }

    getRequestTypeText(type) {
        const typeMap = {
            'weather': '天气查询',
            'translate': '文本翻译',
            'calculate': '数学计算',
            'search': '信息搜索'
        };
        return typeMap[type] || type;
    }

    updateMonitoringStats() {
        document.getElementById('totalRequests').textContent = this.requestCount;

        if (this.responseTimes.length > 0) {
            const avgTime = Math.round(this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length);
            document.getElementById('avgResponseTime').textContent = `${avgTime}ms`;
        }

        const successRate = this.requestCount > 0 ? Math.round((this.successCount / this.requestCount) * 100) : 100;
        document.getElementById('successRate').textContent = `${successRate}%`;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Host层类
class HostLayer {
    constructor() {
        this.requestTypes = {
            weather: '天气查询',
            translate: '文本翻译',
            calculate: '数学计算',
            search: '信息搜索'
        };
    }

    selectRequestType(type) {
        this.updateSendButton();
    }

    updateSendButton() {
        const requestType = document.getElementById('requestType').value;
        const requestContent = document.getElementById('requestContent').value.trim();
        const sendButton = document.getElementById('sendRequest');

        sendButton.disabled = !requestType || !requestContent;
    }

    getRequestData() {
        return {
            type: document.getElementById('requestType').value,
            content: document.getElementById('requestContent').value
        };
    }

    showStatus(message) {
        document.getElementById('responseContent').innerHTML = `<p>${message}</p>`;
    }

    showSuccess(requestData, result) {
        const typeText = this.requestTypes[requestData.type] || requestData.type;

        document.getElementById('responseContent').innerHTML = `
            <div class="success-message">
                <h6>✅ 请求处理成功！</h6>
                <p><strong>请求类型：</strong>${typeText}</p>
                <p><strong>请求内容：</strong>${requestData.content}</p>
                <p><strong>处理结果：</strong></p>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 6px; margin-top: 8px;">
                    ${result}
                </div>
            </div>
        `;
    }

    showError(message) {
        document.getElementById('responseContent').innerHTML = `
            <div class="error-message">
                <h6>❌ 请求失败</h6>
                <p>${message}</p>
            </div>
        `;
    }
}

// Client层类
class ClientLayer {
    addLog(message) {
        const logContent = document.getElementById('clientLog');
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
    }
}

// Server层类
class ServerLayer {
    constructor() {
        this.services = {
            weather: this.weatherService,
            translate: this.translateService,
            calculate: this.calculateService,
            search: this.searchService
        };
    }

    addLog(message) {
        const logContent = document.getElementById('serverLog');
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
    }

    async processService(type, content) {
        const service = this.services[type];
        if (service) {
            return await service.call(this, content);
        }
        return '未知服务类型';
    }

    async weatherService(query) {
        // 模拟天气查询
        await new Promise(resolve => setTimeout(resolve, 500));
        const cities = ['北京', '上海', '广州', '深圳', '杭州'];
        const weathers = ['晴天', '多云', '小雨', '阴天'];
        const temps = [15, 20, 25, 28, 32];

        const city = cities[Math.floor(Math.random() * cities.length)];
        const weather = weathers[Math.floor(Math.random() * weathers.length)];
        const temp = temps[Math.floor(Math.random() * temps.length)];

        return `${city}今日天气：${weather}，温度${temp}°C，湿度65%，风力3级`;
    }

    async translateService(text) {
        // 模拟翻译服务
        await new Promise(resolve => setTimeout(resolve, 800));
        const translations = {
            '你好': 'Hello',
            '谢谢': 'Thank you',
            '再见': 'Goodbye',
            '早上好': 'Good morning',
            '晚安': 'Good night'
        };

        const result = translations[text] || `Translated: "${text}" (模拟翻译结果)`;
        return `翻译结果：${result}`;
    }

    async calculateService(expression) {
        // 模拟计算服务
        await new Promise(resolve => setTimeout(resolve, 300));
        try {
            // 简单的数学表达式计算（仅用于演示）
            const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
            const result = eval(sanitized);
            return `计算结果：${expression} = ${result}`;
        } catch (error) {
            return `计算错误：无法计算表达式 "${expression}"`;
        }
    }

    async searchService(query) {
        // 模拟搜索服务
        await new Promise(resolve => setTimeout(resolve, 600));
        const searchResults = [
            `关于"${query}"的搜索结果：`,
            `1. ${query}相关的百科信息 - 详细介绍了${query}的基本概念和应用`,
            `2. ${query}最新资讯 - 包含最新的${query}相关新闻和动态`,
            `3. ${query}技术文档 - 提供${query}的技术规范和使用指南`,
            `找到约 1,234,567 个相关结果 (用时 0.42 秒)`
        ];

        return searchResults.join('<br>');
    }
}

// 全局函数
function showCoreCode() {
    const modal = document.getElementById('codeModal');
    const codeDisplay = document.getElementById('codeDisplay');

    codeDisplay.textContent = getCoreCode();
    modal.style.display = 'block';
}

function closeCoreCode() {
    const modal = document.getElementById('codeModal');
    modal.style.display = 'none';
}

// 点击模态框外部关闭
window.onclick = function (event) {
    const modal = document.getElementById('codeModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// 初始化系统
let mcpSystem;

document.addEventListener('DOMContentLoaded', function () {
    mcpSystem = new MCPSystem();
    console.log('MCP演示系统已启动');
});
