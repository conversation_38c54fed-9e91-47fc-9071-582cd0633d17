<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transformer注意力机制交互式演示</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧠 Transformer注意力机制交互式演示</h1>
            <p class="subtitle">通过实际操作理解自注意力机制的工作原理</p>
        </header>

        <!-- 知识介绍区域 -->
        <section class="knowledge-section">
            <h2>📚 什么是注意力机制？</h2>
            <div class="knowledge-content">
                <p><strong>注意力机制</strong>是Transformer模型的核心，它模拟了人类注意力的工作方式。当我们阅读一句话时，会自动关注与当前词语最相关的其他词语。</p>
                <p><strong>自注意力机制</strong>让模型能够：</p>
                <ul>
                    <li>🔍 <strong>关注相关信息</strong>：找出句子中哪些词与当前词最相关</li>
                    <li>🔗 <strong>建立长距离依赖</strong>：理解距离较远的词之间的关系</li>
                    <li>⚡ <strong>并行计算</strong>：同时处理所有位置的信息</li>
                </ul>
            </div>
        </section>

        <!-- 步骤指导 -->
        <section class="steps-section">
            <h2>🎯 操作步骤</h2>
            <div class="steps-grid">
                <div class="step-card" id="step1">
                    <h3>步骤1: 输入句子</h3>
                    <p>在下方输入一个句子，体验注意力机制如何分析词语关系</p>
                </div>
                <div class="step-card" id="step2">
                    <h3>步骤2: 生成Q、K、V</h3>
                    <p>将每个词转换为查询(Q)、键(K)、值(V)向量</p>
                </div>
                <div class="step-card" id="step3">
                    <h3>步骤3: 计算注意力分数</h3>
                    <p>计算每个词对其他词的关注程度</p>
                </div>
                <div class="step-card" id="step4">
                    <h3>步骤4: 查看最终结果</h3>
                    <p>观察注意力权重如何影响最终的表示</p>
                </div>
            </div>
        </section>

        <!-- 交互式演示区域 -->
        <section class="demo-section">
            <h2>🚀 开始体验</h2>
            
            <!-- 输入区域 -->
            <div class="input-area">
                <label for="sentence-input">输入句子（建议：我爱学习人工智能）：</label>
                <input type="text" id="sentence-input" placeholder="请输入一个句子..." value="我爱学习人工智能">
                <button id="analyze-btn">🔍 开始分析</button>
            </div>

            <!-- 当前步骤显示 -->
            <div class="current-step">
                <h3 id="current-step-title">准备开始...</h3>
                <p id="current-step-explanation">点击"开始分析"按钮来体验注意力机制的工作过程</p>
            </div>

            <!-- 词语显示区域 -->
            <div class="words-container" id="words-container">
                <!-- 动态生成词语卡片 -->
            </div>

            <!-- QKV矩阵显示区域 -->
            <div class="matrices-container" id="matrices-container" style="display: none;">
                <div class="matrix-section">
                    <h4>🔍 查询矩阵 (Q)</h4>
                    <div class="matrix-explanation">
                        <p><strong>作用</strong>：表示"我想要什么信息"</p>
                        <p><strong>比喻</strong>：就像在图书馆里，你心中想要查找的关键词</p>
                    </div>
                    <div class="matrix" id="q-matrix"></div>
                </div>
                
                <div class="matrix-section">
                    <h4>🔑 键矩阵 (K)</h4>
                    <div class="matrix-explanation">
                        <p><strong>作用</strong>：表示"我能提供什么信息"</p>
                        <p><strong>比喻</strong>：就像图书馆里每本书的标签和索引</p>
                    </div>
                    <div class="matrix" id="k-matrix"></div>
                </div>
                
                <div class="matrix-section">
                    <h4>💎 值矩阵 (V)</h4>
                    <div class="matrix-explanation">
                        <p><strong>作用</strong>：表示"实际的信息内容"</p>
                        <p><strong>比喻</strong>：就像图书馆里书籍的实际内容</p>
                    </div>
                    <div class="matrix" id="v-matrix"></div>
                </div>
            </div>

            <!-- 注意力分数显示 -->
            <div class="attention-scores" id="attention-scores" style="display: none;">
                <h4>📊 注意力分数矩阵</h4>
                <div class="attention-explanation">
                    <p><strong>计算方式</strong>：Q × K^T，然后应用softmax归一化</p>
                    <p><strong>含义</strong>：每个数值表示一个词对另一个词的关注程度（0-1之间）</p>
                    <p><strong>颜色说明</strong>：<span class="color-high">深色</span> = 高关注度，<span class="color-low">浅色</span> = 低关注度</p>
                </div>
                <div class="attention-matrix" id="attention-matrix"></div>
            </div>

            <!-- 最终结果显示 -->
            <div class="final-result" id="final-result" style="display: none;">
                <h4>🎯 最终注意力结果</h4>
                <div class="result-explanation">
                    <p><strong>计算方式</strong>：注意力权重 × 值矩阵 (V)</p>
                    <p><strong>意义</strong>：每个词的新表示，融合了其他相关词的信息</p>
                </div>
                <div class="result-matrix" id="result-matrix"></div>
            </div>

            <!-- 控制按钮 -->
            <div class="controls" id="controls" style="display: none;">
                <button id="prev-step">⬅️ 上一步</button>
                <button id="next-step">➡️ 下一步</button>
                <button id="reset-demo">🔄 重新开始</button>
            </div>
        </section>

        <!-- 深入理解区域 -->
        <section class="understanding-section">
            <h2>🎓 深入理解</h2>
            <div class="understanding-content">
                <div class="concept-card">
                    <h3>🤔 为什么需要Q、K、V？</h3>
                    <p><strong>分工明确</strong>：就像在餐厅点餐，Q是你的需求（"我想要什么"），K是菜单（"有什么可选择的"），V是实际的菜品（"真正的内容"）。</p>
                </div>
                
                <div class="concept-card">
                    <h3>⚡ 为什么比传统RNN更好？</h3>
                    <ul>
                        <li><strong>并行计算</strong>：所有位置同时处理，不需要逐个等待</li>
                        <li><strong>长距离依赖</strong>：直接连接任意两个位置，不会丢失信息</li>
                        <li><strong>可解释性</strong>：注意力权重直观显示了模型的关注点</li>
                    </ul>
                </div>
                
                <div class="concept-card">
                    <h3>🔢 数学本质</h3>
                    <p><strong>核心公式</strong>：Attention(Q,K,V) = softmax(QK^T/√d_k)V</p>
                    <p><strong>直观理解</strong>：用查询去匹配所有的键，得到权重，然后用权重对值进行加权平均。</p>
                </div>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>
