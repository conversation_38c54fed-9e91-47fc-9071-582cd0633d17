// 模型蒸馏核心代码库
const ModelDistillationCodes = {
    overview: {
        title: "模型蒸馏概览核心代码",
        description: "实现教师模型与学生模型对比演示的核心JavaScript代码",
        code: `// 模型蒸馏概览演示核心代码

// 模型数据结构
const modelData = {
    teacher: {
        name: "教师模型",
        parameters: "175B",
        accuracy: 95,
        speed: 20,
        resource: 90,
        layers: 6
    },
    student: {
        name: "学生模型",
        parameters: "7B",
        accuracy: 88,
        speed: 85,
        resource: 25,
        layers: 3
    }
};

// 创建模型可视化
function createModelVisualization(modelType, container) {
    const model = modelData[modelType];

    // 创建模型卡片
    const modelCard = document.createElement('div');
    modelCard.className = \`model-card \${modelType}-model\`;

    // 模型头部信息
    const header = document.createElement('div');
    header.className = 'model-header';
    header.innerHTML = \`
        <h4>\${modelType === 'teacher' ? '👨‍🏫' : '👨‍🎓'} \${model.name}</h4>
        <div class="model-size">参数量: \${model.parameters}</div>
    \`;

    // 模型大脑可视化
    const brain = document.createElement('div');
    brain.className = 'model-brain';
    const layers = document.createElement('div');
    layers.className = 'brain-layers';

    // 创建层级
    for (let i = 0; i < model.layers; i++) {
        const layer = document.createElement('div');
        layer.className = 'layer';
        layer.style.animationDelay = \`\${i * 0.2}s\`;
        layers.appendChild(layer);
    }
    brain.appendChild(layers);

    // 模型统计信息
    const stats = document.createElement('div');
    stats.className = 'model-stats';

    const metrics = [
        { label: '准确率', value: model.accuracy, suffix: '%', type: 'accuracy' },
        { label: '速度', value: model.speed, suffix: modelType === 'teacher' ? '慢' : '快', type: 'speed' },
        { label: '资源消耗', value: model.resource, suffix: modelType === 'teacher' ? '高' : '低', type: 'resource' }
    ];

    metrics.forEach(metric => {
        const statDiv = document.createElement('div');
        statDiv.className = 'stat';
        statDiv.innerHTML = \`
            <span class="stat-label">\${metric.label}</span>
            <div class="stat-bar">
                <div class="stat-fill \${modelType}-\${metric.type}" style="width: \${metric.value}%"></div>
            </div>
            <span class="stat-value">\${metric.suffix}</span>
        \`;
        stats.appendChild(statDiv);
    });

    // 组装模型卡片
    modelCard.appendChild(header);
    modelCard.appendChild(brain);
    modelCard.appendChild(stats);

    return modelCard;
}

// 创建蒸馏箭头动画
function createDistillationArrow() {
    const arrow = document.createElement('div');
    arrow.className = 'distillation-arrow';

    arrow.innerHTML = \`
        <div class="arrow-body">
            <span class="arrow-text">知识蒸馏</span>
            <div class="knowledge-particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>
        </div>
        <div class="arrow-head">→</div>
    \`;

    return arrow;
}

// 蒸馏演示动画
function startDistillationDemo() {
    const teacherModel = document.querySelector('.teacher-model');
    const studentModel = document.querySelector('.student-model');
    const arrow = document.querySelector('.distillation-arrow');

    // 重置动画
    teacherModel.style.transform = 'scale(1)';
    studentModel.style.transform = 'scale(1)';
    arrow.style.transform = 'scale(1)';

    // 开始动画序列
    setTimeout(() => {
        teacherModel.style.transform = 'scale(1.05)';
        teacherModel.style.boxShadow = '0 15px 40px rgba(231, 76, 60, 0.3)';
    }, 500);

    setTimeout(() => {
        arrow.style.transform = 'scale(1.1)';
        const particles = arrow.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
            particle.style.animationDuration = '0.8s';
        });
    }, 1000);

    setTimeout(() => {
        studentModel.style.transform = 'scale(1.05)';
        studentModel.style.boxShadow = '0 15px 40px rgba(39, 174, 96, 0.3)';
    }, 1500);

    setTimeout(() => {
        // 重置状态
        teacherModel.style.transform = 'scale(1)';
        studentModel.style.transform = 'scale(1)';
        arrow.style.transform = 'scale(1)';
        teacherModel.style.boxShadow = '';
        studentModel.style.boxShadow = '';
    }, 3000);
}

// 重置蒸馏演示
function resetDistillationDemo() {
    const teacherModel = document.querySelector('.teacher-model');
    const studentModel = document.querySelector('.student-model');
    const arrow = document.querySelector('.distillation-arrow');

    // 重置所有样式
    teacherModel.style.transform = 'scale(1)';
    studentModel.style.transform = 'scale(1)';
    arrow.style.transform = 'scale(1)';
    teacherModel.style.boxShadow = '';
    studentModel.style.boxShadow = '';

    // 重置粒子动画
    const particles = arrow.querySelectorAll('.particle');
    particles.forEach(particle => {
        particle.style.animationDuration = '1.5s';
    });
}

// 初始化模型对比演示
function initializeModelComparison() {
    const container = document.querySelector('.model-comparison');
    if (!container) return;

    // 清空容器
    container.innerHTML = '';

    // 创建教师模型
    const teacherModel = createModelVisualization('teacher', container);
    container.appendChild(teacherModel);

    // 创建蒸馏箭头
    const arrow = createDistillationArrow();
    container.appendChild(arrow);

    // 创建学生模型
    const studentModel = createModelVisualization('student', container);
    container.appendChild(studentModel);
}

// 使用示例
document.addEventListener('DOMContentLoaded', function() {
    // 初始化模型对比演示
    initializeModelComparison();

    // 绑定演示按钮事件
    const startBtn = document.querySelector('button[onclick="startDistillationDemo()"]');
    const resetBtn = document.querySelector('button[onclick="resetDistillationDemo()"]');

    if (startBtn) {
        startBtn.addEventListener('click', startDistillationDemo);
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', resetDistillationDemo);
    }
});

// 导出函数供全局使用
window.startDistillationDemo = startDistillationDemo;
window.resetDistillationDemo = resetDistillationDemo;
window.initializeModelComparison = initializeModelComparison;`
    },

    temperature: {
        title: "温度参数演示核心代码",
        description: "实现温度参数对softmax概率分布影响的可视化演示",
        code: `// 温度参数演示核心代码

// 原始logits数据
const logitsData = [
    { label: '猫', value: 4.0 },
    { label: '狗', value: 3.0 },
    { label: '鸟', value: 2.0 },
    { label: '鱼', value: 1.0 }
];

// Softmax函数（带温度参数）
function softmaxWithTemperature(logits, temperature) {
    // 计算 exp(logit/T)
    const expValues = logits.map(logit => Math.exp(logit / temperature));

    // 计算总和
    const sum = expValues.reduce((acc, val) => acc + val, 0);

    // 计算概率
    const probabilities = expValues.map(exp => exp / sum);

    return probabilities;
}

// 更新概率显示
function updateProbabilityDisplay(temperature) {
    const logits = logitsData.map(item => item.value);
    const probabilities = softmaxWithTemperature(logits, temperature);

    const probabilityDisplay = document.getElementById('probability-display');
    if (!probabilityDisplay) return;

    // 清空显示区域
    probabilityDisplay.innerHTML = '';

    // 创建概率条
    logitsData.forEach((item, index) => {
        const prob = probabilities[index];
        const percentage = (prob * 100).toFixed(1);

        const probBar = document.createElement('div');
        probBar.className = 'prob-bar';

        probBar.innerHTML = \`
            <span class="label">\${item.label}</span>
            <div class="bar">
                <div class="fill" style="width: \${prob * 100}%"></div>
            </div>
            <span class="value">\${percentage}%</span>
        \`;

        probabilityDisplay.appendChild(probBar);
    });
}

// 温度滑块事件处理
function initializeTemperatureControl() {
    const temperatureSlider = document.getElementById('temperature-slider');
    const temperatureValue = document.getElementById('temperature-value');

    if (!temperatureSlider || !temperatureValue) return;

    // 初始化显示
    updateProbabilityDisplay(parseFloat(temperatureSlider.value));

    // 滑块变化事件
    temperatureSlider.addEventListener('input', function() {
        const temperature = parseFloat(this.value);
        temperatureValue.textContent = temperature.toFixed(1);
        updateProbabilityDisplay(temperature);

        // 更新解释卡片的样式
        updateExplanationCards(temperature);
    });
}

// 更新解释卡片样式
function updateExplanationCards(temperature) {
    const lowTempCard = document.querySelector('.low-temp');
    const highTempCard = document.querySelector('.high-temp');

    if (!lowTempCard || !highTempCard) return;

    // 根据温度值调整卡片透明度
    if (temperature <= 1.5) {
        lowTempCard.style.opacity = '1';
        lowTempCard.style.transform = 'scale(1.05)';
        highTempCard.style.opacity = '0.7';
        highTempCard.style.transform = 'scale(1)';
    } else if (temperature >= 3) {
        lowTempCard.style.opacity = '0.7';
        lowTempCard.style.transform = 'scale(1)';
        highTempCard.style.opacity = '1';
        highTempCard.style.transform = 'scale(1.05)';
    } else {
        lowTempCard.style.opacity = '0.8';
        lowTempCard.style.transform = 'scale(1)';
        highTempCard.style.opacity = '0.8';
        highTempCard.style.transform = 'scale(1)';
    }
}

// 温度效果动画演示
function animateTemperatureEffect() {
    const slider = document.getElementById('temperature-slider');
    if (!slider) return;

    const originalValue = parseFloat(slider.value);
    const temperatures = [0.1, 1.0, 3.0, 5.0, 10.0];
    let currentIndex = 0;

    // 禁用滑块
    slider.disabled = true;

    function animateStep() {
        if (currentIndex >= temperatures.length) {
            // 动画结束，恢复原始值
            slider.value = originalValue;
            slider.disabled = false;
            updateProbabilityDisplay(originalValue);
            document.getElementById('temperature-value').textContent = originalValue.toFixed(1);
            updateExplanationCards(originalValue);
            return;
        }

        const temp = temperatures[currentIndex];
        slider.value = temp;
        document.getElementById('temperature-value').textContent = temp.toFixed(1);
        updateProbabilityDisplay(temp);
        updateExplanationCards(temp);

        currentIndex++;
        setTimeout(animateStep, 1500); // 每1.5秒切换一次
    }

    animateStep();
}

// 计算信息熵（衡量不确定性）
function calculateEntropy(probabilities) {
    return -probabilities.reduce((entropy, prob) => {
        return entropy + (prob > 0 ? prob * Math.log2(prob) : 0);
    }, 0);
}

// 分析温度对分布的影响
function analyzeTemperatureEffect(temperature) {
    const logits = logitsData.map(item => item.value);
    const probabilities = softmaxWithTemperature(logits, temperature);
    const entropy = calculateEntropy(probabilities);

    return {
        probabilities,
        entropy,
        maxProb: Math.max(...probabilities),
        minProb: Math.min(...probabilities),
        spread: Math.max(...probabilities) - Math.min(...probabilities)
    };
}

// 温度参数教学演示
function demonstrateTemperatureTeaching() {
    console.log('=== 温度参数教学演示 ===');

    const temperatures = [0.1, 1.0, 3.0, 10.0];

    temperatures.forEach(temp => {
        const analysis = analyzeTemperatureEffect(temp);
        console.log(\`\\n温度 T = \${temp}:\`);
        console.log(\`  信息熵: \${analysis.entropy.toFixed(3)}\`);
        console.log(\`  最大概率: \${(analysis.maxProb * 100).toFixed(1)}%\`);
        console.log(\`  概率分布:\`, analysis.probabilities.map(p => (p * 100).toFixed(1) + '%'));
    });
}

// 初始化温度演示
function initializeTemperatureDemo() {
    initializeTemperatureControl();

    // 运行教学演示
    demonstrateTemperatureTeaching();
}

// 使用示例
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在温度演示页面
    if (document.getElementById('temperature-slider')) {
        initializeTemperatureDemo();
    }
});

// 导出函数供全局使用
window.animateTemperatureEffect = animateTemperatureEffect;
window.updateProbabilityDisplay = updateProbabilityDisplay;
window.softmaxWithTemperature = softmaxWithTemperature;`
    },

    process: {
        title: "蒸馏过程演示核心代码",
        description: "实现完整的模型蒸馏过程可视化演示，包括数据准备、教师推理、学生训练和性能评估",
        code: `// 蒸馏过程演示核心代码

// 蒸馏过程状态管理
const distillationProcess = {
    currentStep: 0,
    steps: [
        { id: 'step-1', name: '数据准备', duration: 2000 },
        { id: 'step-2', name: '教师模型推理', duration: 3000 },
        { id: 'step-3', name: '学生模型训练', duration: 4000 },
        { id: 'step-4', name: '性能评估', duration: 2000 }
    ],
    isRunning: false
};

// 蒸馏损失函数计算
function calculateDistillationLoss(studentLogits, teacherLogits, temperature, alpha) {
    // 计算软目标损失（KL散度）
    const studentProbs = softmax(studentLogits.map(x => x / temperature));
    const teacherProbs = softmax(teacherLogits.map(x => x / temperature));

    const klDivergence = teacherProbs.reduce((loss, teacherProb, i) => {
        const studentProb = studentProbs[i];
        return loss + teacherProb * Math.log(teacherProb / (studentProb + 1e-8));
    }, 0);

    const softLoss = klDivergence * (temperature * temperature);

    // 计算硬目标损失（交叉熵）
    const hardLoss = -Math.log(studentProbs[0] + 1e-8); // 假设真实标签是第一个类别

    // 总损失
    const totalLoss = alpha * softLoss + (1 - alpha) * hardLoss;

    return {
        softLoss,
        hardLoss,
        totalLoss,
        klDivergence
    };
}

// Softmax函数
function softmax(logits) {
    const maxLogit = Math.max(...logits);
    const expValues = logits.map(x => Math.exp(x - maxLogit));
    const sum = expValues.reduce((a, b) => a + b, 0);
    return expValues.map(x => x / sum);
}

// 高亮显示当前步骤
function highlightStep(stepIndex) {
    const steps = document.querySelectorAll('.process-step');

    steps.forEach((step, index) => {
        if (index === stepIndex) {
            step.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
            step.style.color = 'white';
            step.style.transform = 'scale(1.05)';
            step.style.boxShadow = '0 15px 40px rgba(102, 126, 234, 0.3)';
        } else if (index < stepIndex) {
            step.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
            step.style.color = 'white';
            step.style.transform = 'scale(1)';
            step.style.boxShadow = '0 8px 25px rgba(39, 174, 96, 0.2)';
        } else {
            step.style.background = 'white';
            step.style.color = '#333';
            step.style.transform = 'scale(1)';
            step.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        }
    });
}

// 动画显示数据流
function animateDataFlow() {
    const dataItems = document.querySelectorAll('.data-item');

    dataItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
            item.style.color = 'white';
            item.style.transform = 'scale(1.1)';

            setTimeout(() => {
                item.style.background = '#f8f9fa';
                item.style.color = '#5a6c7d';
                item.style.transform = 'scale(1)';
            }, 800);
        }, index * 300);
    });
}

// 动画显示教师推理过程
function animateTeacherInference() {
    const inputData = document.querySelector('.input-data');
    const softTarget = document.querySelector('.soft-target');
    const hardTarget = document.querySelector('.hard-target');

    // 输入数据高亮
    setTimeout(() => {
        inputData.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
        inputData.style.color = 'white';
        inputData.style.transform = 'scale(1.05)';
    }, 500);

    // 软目标生成
    setTimeout(() => {
        softTarget.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
        softTarget.style.color = 'white';
        softTarget.style.transform = 'scale(1.1)';
    }, 1500);

    // 硬目标生成
    setTimeout(() => {
        hardTarget.style.background = 'linear-gradient(135deg, #9b59b6, #8e44ad)';
        hardTarget.style.color = 'white';
        hardTarget.style.transform = 'scale(1.1)';
    }, 2000);

    // 重置样式
    setTimeout(() => {
        inputData.style.background = '#e3f2fd';
        inputData.style.color = '#1976d2';
        inputData.style.transform = 'scale(1)';

        softTarget.style.background = '#fff3e0';
        softTarget.style.color = '#f57c00';
        softTarget.style.transform = 'scale(1)';

        hardTarget.style.background = '#f3e5f5';
        hardTarget.style.color = '#7b1fa2';
        hardTarget.style.transform = 'scale(1)';
    }, 2800);
}

// 动画显示学生训练过程
function animateStudentTraining() {
    const distillationLoss = document.querySelector('.loss-fill.distillation');
    const taskLoss = document.querySelector('.loss-fill.task');

    // 模拟训练过程中损失的变化
    let distillationWidth = 60;
    let taskWidth = 40;

    const trainingInterval = setInterval(() => {
        // 蒸馏损失逐渐减少
        distillationWidth = Math.max(20, distillationWidth - 2);
        taskWidth = Math.max(15, taskWidth - 1);

        if (distillationLoss) {
            distillationLoss.style.width = distillationWidth + '%';
        }
        if (taskLoss) {
            taskLoss.style.width = taskWidth + '%';
        }

        if (distillationWidth <= 20 && taskWidth <= 15) {
            clearInterval(trainingInterval);

            // 重置到初始状态
            setTimeout(() => {
                if (distillationLoss) distillationLoss.style.width = '60%';
                if (taskLoss) taskLoss.style.width = '40%';
            }, 1000);
        }
    }, 200);
}

// 动画显示性能评估
function animatePerformanceEvaluation() {
    const metrics = document.querySelectorAll('.metric');

    metrics.forEach((metric, index) => {
        setTimeout(() => {
            metric.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
            metric.style.color = 'white';
            metric.style.transform = 'scale(1.05)';

            setTimeout(() => {
                metric.style.background = '#f8f9fa';
                metric.style.color = '#333';
                metric.style.transform = 'scale(1)';
            }, 1000);
        }, index * 300);
    });
}

// 开始蒸馏过程演示
function startProcessDemo() {
    if (distillationProcess.isRunning) return;

    distillationProcess.isRunning = true;
    distillationProcess.currentStep = 0;

    function executeStep() {
        const step = distillationProcess.steps[distillationProcess.currentStep];
        if (!step) {
            distillationProcess.isRunning = false;
            return;
        }

        // 高亮当前步骤
        highlightStep(distillationProcess.currentStep);

        // 执行步骤特定的动画
        switch (step.id) {
            case 'step-1':
                animateDataFlow();
                break;
            case 'step-2':
                animateTeacherInference();
                break;
            case 'step-3':
                animateStudentTraining();
                break;
            case 'step-4':
                animatePerformanceEvaluation();
                break;
        }

        // 移动到下一步
        setTimeout(() => {
            distillationProcess.currentStep++;
            executeStep();
        }, step.duration);
    }

    executeStep();
}

// 重置蒸馏过程演示
function resetProcessDemo() {
    distillationProcess.isRunning = false;
    distillationProcess.currentStep = 0;

    // 重置所有步骤样式
    const steps = document.querySelectorAll('.process-step');
    steps.forEach(step => {
        step.style.background = 'white';
        step.style.color = '#333';
        step.style.transform = 'scale(1)';
        step.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
    });

    // 重置数据项样式
    const dataItems = document.querySelectorAll('.data-item');
    dataItems.forEach(item => {
        item.style.background = '#f8f9fa';
        item.style.color = '#5a6c7d';
        item.style.transform = 'scale(1)';
    });

    // 重置教师推理样式
    const inputData = document.querySelector('.input-data');
    const softTarget = document.querySelector('.soft-target');
    const hardTarget = document.querySelector('.hard-target');

    if (inputData) {
        inputData.style.background = '#e3f2fd';
        inputData.style.color = '#1976d2';
        inputData.style.transform = 'scale(1)';
    }

    if (softTarget) {
        softTarget.style.background = '#fff3e0';
        softTarget.style.color = '#f57c00';
        softTarget.style.transform = 'scale(1)';
    }

    if (hardTarget) {
        hardTarget.style.background = '#f3e5f5';
        hardTarget.style.color = '#7b1fa2';
        hardTarget.style.transform = 'scale(1)';
    }

    // 重置损失条
    const distillationLoss = document.querySelector('.loss-fill.distillation');
    const taskLoss = document.querySelector('.loss-fill.task');

    if (distillationLoss) distillationLoss.style.width = '60%';
    if (taskLoss) taskLoss.style.width = '40%';

    // 重置评估指标
    const metrics = document.querySelectorAll('.metric');
    metrics.forEach(metric => {
        metric.style.background = '#f8f9fa';
        metric.style.color = '#333';
        metric.style.transform = 'scale(1)';
    });
}

// 蒸馏过程教学演示
function demonstrateDistillationProcess() {
    console.log('=== 蒸馏过程教学演示 ===');

    // 模拟教师和学生模型的logits
    const teacherLogits = [4.0, 3.0, 2.0, 1.0];
    const studentLogits = [3.5, 2.8, 1.9, 1.2];
    const temperature = 3.0;
    const alpha = 0.7;

    const loss = calculateDistillationLoss(studentLogits, teacherLogits, temperature, alpha);

    console.log('教师模型 logits:', teacherLogits);
    console.log('学生模型 logits:', studentLogits);
    console.log('温度参数:', temperature);
    console.log('蒸馏权重 α:', alpha);
    console.log('\\n损失计算结果:');
    console.log('  软目标损失:', loss.softLoss.toFixed(4));
    console.log('  硬目标损失:', loss.hardLoss.toFixed(4));
    console.log('  总损失:', loss.totalLoss.toFixed(4));
    console.log('  KL散度:', loss.klDivergence.toFixed(4));
}

// 初始化蒸馏过程演示
function initializeProcessDemo() {
    // 运行教学演示
    demonstrateDistillationProcess();
}

// 使用示例
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在蒸馏过程页面
    if (document.querySelector('.distillation-process')) {
        initializeProcessDemo();
    }
});

// 导出函数供全局使用
window.startProcessDemo = startProcessDemo;
window.resetProcessDemo = resetProcessDemo;
window.calculateDistillationLoss = calculateDistillationLoss;`
    }
};