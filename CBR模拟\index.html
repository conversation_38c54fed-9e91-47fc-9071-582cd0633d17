<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBR基于案例推理 - 交互式学习演示</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="container">
        <!-- 标题和说明 -->
        <header class="header">
            <h1>🧠 CBR基于案例推理演示</h1>
            <p class="subtitle">体验从"表层感知"到"深度推理"的AI认知进阶过程</p>
        </header>

        <!-- 核心概念说明 -->
        <section class="concept-section">
            <div class="concept-card">
                <h3>💡 什么是CBR？</h3>
                <p><strong>基于案例推理（Case-Based Reasoning,
                        CBR）</strong>是一种模拟人类思考过程的AI方法，通过<strong>积累过去案例、检索相似经验、调整适应方案</strong>来解决新问题。</p>
                <div class="analogy">
                    <span class="analogy-icon">🎯</span>
                    <p><strong>核心理念：</strong>不是从零推理，而是以案例为知识，以相似性为依据，以适应调整为核心。</p>
                </div>
            </div>
        </section>

        <!-- CBR四大流程展示 -->
        <section class="cbr-process">
            <h3>🔄 CBR四大核心流程</h3>
            <div class="process-container">
                <div class="process-step" id="step1">
                    <div class="step-number">1</div>
                    <div class="step-title">检索 (Retrieve)</div>
                    <div class="step-desc">从案例库中找到相似案例</div>
                    <div class="step-status">等待开始</div>
                </div>
                <div class="process-arrow">→</div>

                <div class="process-step" id="step2">
                    <div class="step-number">2</div>
                    <div class="step-title">重用 (Reuse)</div>
                    <div class="step-desc">调整相似案例适应新问题</div>
                    <div class="step-status">等待开始</div>
                </div>
                <div class="process-arrow">→</div>

                <div class="process-step" id="step3">
                    <div class="step-number">3</div>
                    <div class="step-title">修正 (Revise)</div>
                    <div class="step-desc">根据反馈优化解决方案</div>
                    <div class="step-status">等待开始</div>
                </div>
                <div class="process-arrow">→</div>

                <div class="process-step" id="step4">
                    <div class="step-number">4</div>
                    <div class="step-title">保留 (Retain)</div>
                    <div class="step-desc">将新案例存入案例库</div>
                    <div class="step-status">等待开始</div>
                </div>
            </div>
        </section>

        <!-- 交互式演示区域 -->
        <div class="demo-area">
            <!-- 问题输入区 -->
            <div class="problem-input">
                <h3>🎯 新问题场景</h3>
                <div class="scenario-selector">
                    <label>选择一个问题场景：</label>
                    <select id="scenarioSelect">
                        <option value="">-- 请选择场景 --</option>
                        <option value="medical">医疗诊断：患者症状分析</option>
                        <option value="legal">法律咨询：案件类型判断</option>
                        <option value="technical">技术支持：故障排除</option>
                        <option value="education">教育推荐：学习路径规划</option>
                    </select>
                </div>
                <div class="problem-details" id="problemDetails">
                    <p>请先选择一个问题场景</p>
                </div>
                <button class="start-btn" id="startCBR" disabled>开始CBR推理过程</button>
            </div>

            <!-- 案例库展示 -->
            <div class="case-library">
                <h3>📚 案例库</h3>
                <div class="case-count">当前案例数量: <span id="caseCount">0</span></div>
                <div class="cases-container" id="casesContainer">
                    <div class="empty-state">案例库为空，请先选择场景</div>
                </div>
            </div>
        </div>

        <!-- 推理过程展示 -->
        <div class="reasoning-area" id="reasoningArea" style="display: none;">
            <h3>🔍 CBR推理过程实时展示</h3>

            <!-- 当前步骤详情 -->
            <div class="current-step" id="currentStep">
                <div class="step-header">
                    <span class="step-icon">🔍</span>
                    <span class="step-name">准备开始</span>
                </div>
                <div class="step-content">
                    <p>点击"开始CBR推理过程"按钮开始演示</p>
                </div>
            </div>

            <!-- 相似度计算展示 -->
            <div class="similarity-display" id="similarityDisplay" style="display: none;">
                <h4>📊 相似度计算结果</h4>
                <div class="similarity-list" id="similarityList"></div>
            </div>

            <!-- 解决方案展示 -->
            <div class="solution-display" id="solutionDisplay" style="display: none;">
                <h4>💡 推理结果</h4>
                <div class="solution-content" id="solutionContent"></div>
            </div>

            <!-- 反馈区域 -->
            <div class="feedback-area" id="feedbackArea" style="display: none;">
                <h4>📝 解决方案反馈</h4>
                <div class="feedback-options">
                    <button class="feedback-btn success" onclick="provideFeedback('success')">✅ 解决成功</button>
                    <button class="feedback-btn partial" onclick="provideFeedback('partial')">⚠️ 部分有效</button>
                    <button class="feedback-btn failure" onclick="provideFeedback('failure')">❌ 解决失败</button>
                </div>
                <div class="feedback-result" id="feedbackResult"></div>
            </div>
        </div>

        <!-- 学习进度展示 -->
        <div class="learning-progress">
            <h3>📈 系统学习进度</h3>
            <div class="progress-stats">
                <div class="stat-item">
                    <span class="stat-label">累计案例</span>
                    <span class="stat-value" id="totalCases">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">成功解决</span>
                    <span class="stat-value" id="successCases">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">推理准确率</span>
                    <span class="stat-value" id="accuracyRate">0%</span>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <!-- 知识点总结 -->
        <section class="knowledge-section">
            <h3>📚 CBR核心知识点</h3>
            <div class="knowledge-grid">
                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔍</span>
                        <h4>检索 (Retrieve)</h4>
                    </div>
                    <p><strong>目标：</strong>从案例库中找到与当前问题最相似的历史案例</p>
                    <p><strong>关键技术：</strong>相似度计算、特征匹配、索引优化</p>
                    <p><strong>核心价值：</strong>利用过往经验，避免从零开始思考</p>
                </div>

                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔄</span>
                        <h4>重用 (Reuse)</h4>
                    </div>
                    <p><strong>目标：</strong>将检索到的案例解决方案适配到新问题</p>
                    <p><strong>关键技术：</strong>案例适应、参数调整、方案转换</p>
                    <p><strong>核心价值：</strong>快速生成候选解决方案</p>
                </div>

                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">⚙️</span>
                        <h4>修正 (Revise)</h4>
                    </div>
                    <p><strong>目标：</strong>根据实际反馈优化和完善解决方案</p>
                    <p><strong>关键技术：</strong>错误分析、方案调整、质量评估</p>
                    <p><strong>核心价值：</strong>持续改进，提高解决方案质量</p>
                </div>

                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">💾</span>
                        <h4>保留 (Retain)</h4>
                    </div>
                    <p><strong>目标：</strong>将新的成功案例存储到案例库中</p>
                    <p><strong>关键技术：</strong>案例索引、知识提取、库维护</p>
                    <p><strong>核心价值：</strong>积累经验，增强系统推理能力</p>
                </div>

                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🎯</span>
                        <h4>相似性度量</h4>
                    </div>
                    <p><strong>目标：</strong>准确计算案例间的相似程度</p>
                    <p><strong>关键技术：</strong>特征权重、距离函数、语义匹配</p>
                    <p><strong>核心价值：</strong>确保检索到真正有用的相似案例</p>
                </div>

                <div class="knowledge-item">
                    <div class="knowledge-header">
                        <span class="knowledge-icon">🔄</span>
                        <h4>循环学习机制</h4>
                    </div>
                    <p><strong>目标：</strong>通过不断循环提升系统智能水平</p>
                    <p><strong>关键技术：</strong>反馈循环、增量学习、知识更新</p>
                    <p><strong>核心价值：</strong>系统越用越聪明，接近真实智能</p>
                </div>
            </div>
        </section>

        <!-- 核心代码按钮 -->
        <div class="code-section">
            <button class="code-btn" onclick="showCoreCode()">
                🔍 实现该效果的核心JS代码
            </button>
        </div>
    </div>

    <!-- 代码展示模态框 -->
    <div class="modal" id="codeModal">
        <div class="modal-content">
            <span class="close" onclick="closeCoreCode()">&times;</span>
            <h3>CBR系统核心实现代码</h3>
            <pre id="codeDisplay"></pre>
        </div>
    </div>

    <script src="main.js"></script>
    <script src="code.js"></script>
</body>

</html>