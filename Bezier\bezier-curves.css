/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

/* 背景动画 */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-curve {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 20s infinite linear;
}

.floating-curve:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-curve:nth-child(2) {
    top: 60%;
    right: 10%;
    animation-delay: -7s;
}

.floating-curve:nth-child(3) {
    bottom: 20%;
    left: 30%;
    animation-delay: -14s;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.1;
    }

    100% {
        transform: translateY(0px) rotate(360deg);
        opacity: 0.3;
    }
}

/* 主容器 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* 标题区域 */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.main-title {
    font-size: 2.5em;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.title-icon {
    font-size: 1.2em;
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

.title-subtitle {
    font-size: 0.4em;
    color: #7f8c8d;
    font-weight: 400;
    display: block;
    margin-top: 5px;
}

.header-description {
    font-size: 1.1em;
    color: #5a6c7d;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* 导航标签 */
.curve-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 1em;
    font-weight: 600;
    color: #5a6c7d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.tab-icon {
    font-size: 1.2em;
}

/* 主要内容区域 */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 30px;
}

.curve-section {
    display: none;
}

.curve-section.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* 画布容器 */
.canvas-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.canvas-title {
    font-size: 1.3em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

canvas {
    width: 100%;
    height: auto;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: white;
    cursor: crosshair;
    transition: box-shadow 0.3s ease;
}

canvas:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* 控制面板 */
.canvas-controls {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.control-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-icon {
    font-size: 1.1em;
}

.speed-control {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
}

.speed-control label {
    font-weight: 600;
    color: #5a6c7d;
}

.speed-control input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.speed-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    cursor: pointer;
}

.toggle-control {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
}

.toggle-control label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #5a6c7d;
    cursor: pointer;
}

/* 知识面板 */
.knowledge-panel {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-height: 600px;
    overflow-y: auto;
}

.knowledge-title {
    font-size: 1.3em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.knowledge-section {
    margin-bottom: 25px;
}

.knowledge-section h4 {
    font-size: 1.1em;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.knowledge-section p {
    line-height: 1.6;
    color: #5a6c7d;
    margin-bottom: 10px;
}

/* 公式框 */
.formula-box {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 15px 0;
}

.formula {
    font-family: 'Courier New', monospace;
    font-size: 1.1em;
    font-weight: bold;
    text-align: center;
    margin: 10px 0;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px;
    border-radius: 5px;
}

.formula-desc {
    font-size: 0.9em;
    text-align: center;
    opacity: 0.9;
    font-style: italic;
}

/* 列表样式 */
.feature-list,
.application-list {
    list-style: none;
    padding-left: 0;
}

.feature-list li,
.application-list li {
    padding: 8px 0;
    border-left: 3px solid #667eea;
    padding-left: 15px;
    margin-bottom: 8px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 0 5px 5px 0;
}

/* 提示框 */
.tips-box {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
}

.tips-box p {
    margin-bottom: 8px;
    color: white;
}

.warning-box {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    color: #333;
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
}

.warning-box p {
    margin-bottom: 8px;
    color: #333;
}

/* 底部 */
.footer {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    color: #5a6c7d;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.footer p {
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .main-title {
        font-size: 2em;
    }

    .curve-tabs {
        gap: 5px;
    }

    .tab-btn {
        padding: 12px 20px;
        font-size: 0.9em;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        padding: 30px 20px;
    }

    .main-title {
        font-size: 1.8em;
        flex-direction: column;
        gap: 10px;
    }

    .curve-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .main-content {
        padding: 20px;
    }

    .knowledge-panel {
        max-height: none;
    }

    canvas {
        height: 300px;
    }
}

/* 滚动条样式 */
.knowledge-panel::-webkit-scrollbar {
    width: 8px;
}

.knowledge-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.knowledge-panel::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

.knowledge-panel::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* 核心代码按钮样式 */
.core-code-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
}

.core-code-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226) !important;
}

/* 核心代码模态框样式 */
.code-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.code-modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 900px;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.code-modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-modal-header h3 {
    margin: 0;
    font-size: 1.3em;
    font-weight: 600;
}

.code-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 2em;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.code-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.code-modal-body {
    padding: 30px;
    max-height: calc(90vh - 100px);
    overflow-y: auto;
}

.code-description {
    font-size: 1.1em;
    color: #5a6c7d;
    margin-bottom: 20px;
    line-height: 1.6;
    text-align: center;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
}

.code-container {
    background: #1a1a1a;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.code-header {
    background: #2d2d2d;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #404040;
}

.code-language {
    color: #ffd700;
    font-weight: 600;
    font-size: 0.9em;
}

.copy-code-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-code-btn:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
    transform: translateY(-1px);
}

.code-content {
    background: #1a1a1a;
    color: #e2e8f0;
    padding: 25px;
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.6;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 代码语法高亮 */
.code-content code {
    color: #e2e8f0;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .code-modal-content {
        width: 95%;
        max-height: 95%;
    }

    .code-modal-header {
        padding: 15px 20px;
    }

    .code-modal-header h3 {
        font-size: 1.1em;
    }

    .code-modal-body {
        padding: 20px;
    }

    .code-content {
        padding: 20px;
        font-size: 0.8em;
    }

    .code-header {
        padding: 12px 15px;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .copy-code-btn {
        width: 100%;
        text-align: center;
    }
}