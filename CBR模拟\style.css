/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 0;
}

/* 概念说明区域 */
.concept-section {
    margin-bottom: 30px;
}

.concept-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #667eea;
}

.concept-card h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.concept-card p {
    line-height: 1.6;
    margin-bottom: 15px;
}

.analogy {
    background: #f8f9ff;
    padding: 15px;
    border-radius: 10px;
    border-left: 3px solid #667eea;
    margin-top: 15px;
}

.analogy-icon {
    font-size: 1.5em;
    margin-right: 10px;
}

/* CBR流程展示 */
.cbr-process {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.cbr-process h3 {
    color: #667eea;
    margin-bottom: 25px;
    font-size: 1.4em;
    text-align: center;
}

.process-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

.process-step {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    min-width: 200px;
    flex: 1;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    position: relative;
}

.process-step.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.process-step.completed {
    border-color: #4CAF50;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
}

.step-number {
    background: #667eea;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-weight: bold;
    font-size: 1.2em;
}

.process-step.active .step-number,
.process-step.completed .step-number {
    background: rgba(255, 255, 255, 0.3);
}

.step-title {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.step-desc {
    font-size: 0.9em;
    margin-bottom: 10px;
    opacity: 0.8;
}

.step-status {
    font-size: 0.8em;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    display: inline-block;
}

.process-step.active .step-status,
.process-step.completed .step-status {
    background: rgba(255, 255, 255, 0.3);
}

.process-arrow {
    font-size: 2em;
    color: #667eea;
    font-weight: bold;
    margin: 0 10px;
}

/* 演示区域 */
.demo-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.problem-input,
.case-library {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.problem-input h3,
.case-library h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.scenario-selector {
    margin-bottom: 20px;
}

.scenario-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.scenario-selector select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1em;
    background: white;
    transition: border-color 0.3s ease;
}

.scenario-selector select:focus {
    outline: none;
    border-color: #667eea;
}

.problem-details {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.start-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: bold;
    transition: all 0.3s ease;
    width: 100%;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.start-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.start-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

.case-count {
    color: #666;
    margin-bottom: 15px;
    font-size: 0.9em;
}

.cases-container {
    max-height: 300px;
    overflow-y: auto;
}

.empty-state {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 40px 20px;
}

.case-item {
    background: #f8f9ff;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.case-item:hover {
    background: #f0f2ff;
    transform: translateX(5px);
}

.case-item.highlighted {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.02);
}

.case-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.case-similarity {
    font-size: 0.8em;
    color: #666;
    float: right;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 8px;
    border-radius: 10px;
}

.case-item.highlighted .case-similarity {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* 推理过程展示 */
.reasoning-area {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.reasoning-area h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.current-step {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.step-icon {
    font-size: 1.5em;
    margin-right: 10px;
}

.step-name {
    font-weight: bold;
    font-size: 1.2em;
    color: #667eea;
}

.step-content {
    line-height: 1.6;
}

.similarity-display,
.solution-display,
.feedback-area {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.similarity-display h4,
.solution-display h4,
.feedback-area h4 {
    color: #667eea;
    margin-bottom: 15px;
}

.similarity-item {
    background: white;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.similarity-score {
    background: #667eea;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9em;
}

.solution-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

.feedback-options {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.feedback-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    flex: 1;
}

.feedback-btn.success {
    background: #4CAF50;
    color: white;
}

.feedback-btn.partial {
    background: #FF9800;
    color: white;
}

.feedback-btn.failure {
    background: #f44336;
    color: white;
}

.feedback-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.feedback-result {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
}

/* 学习进度 */
.learning-progress {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.learning-progress h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    background: #f8f9ff;
    padding: 15px;
    border-radius: 10px;
}

.stat-label {
    display: block;
    color: #666;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.5em;
    font-weight: bold;
    color: #667eea;
}

.progress-bar {
    background: #e0e0e0;
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 5px;
}

/* 知识点区域样式 */
.knowledge-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.knowledge-section h3 {
    color: #667eea;
    margin-bottom: 25px;
    font-size: 1.4em;
    text-align: center;
}

.knowledge-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.knowledge-item {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    transition: transform 0.3s ease;
}

.knowledge-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.knowledge-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.knowledge-icon {
    font-size: 1.5em;
    margin-right: 12px;
}

.knowledge-item h4 {
    color: #667eea;
    margin: 0;
    font-size: 1.1em;
}

.knowledge-item p {
    margin-bottom: 8px;
    line-height: 1.5;
    font-size: 0.95em;
}

.knowledge-item p strong {
    color: #667eea;
}

/* 代码展示区域 */
.code-section {
    text-align: center;
    margin-bottom: 30px;
}

.code-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.code-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #667eea;
}

.modal-content h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.4em;
}

.modal-content pre {
    background: #1a1a1a;
    color: #f8f8f2;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

.processing {
    animation: pulse 1.5s infinite;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideInUp 0.5s ease;
}

@keyframes highlight {
    0% {
        background-color: transparent;
    }

    50% {
        background-color: rgba(102, 126, 234, 0.2);
    }

    100% {
        background-color: transparent;
    }
}

.highlight-animation {
    animation: highlight 1s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .demo-area {
        grid-template-columns: 1fr;
    }

    .process-container {
        flex-direction: column;
    }

    .process-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }

    .knowledge-grid {
        grid-template-columns: 1fr;
    }

    .progress-stats {
        grid-template-columns: 1fr;
    }

    .feedback-options {
        flex-direction: column;
    }

    .header h1 {
        font-size: 2em;
    }

    .container {
        padding: 10px;
    }

    .process-step {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .similarity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .case-similarity {
        float: none;
        align-self: flex-end;
    }
}