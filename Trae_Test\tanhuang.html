<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <title>easeOutElastic 动画示例</title>
    <style>
        /* ====== 1. 通用页面排版（非必需，可根据项目调整） ====== */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, Helvetica, sans-serif;
            background: #f2f6ff;
        }

        /* ====== 2. 关键帧：模拟 easeOutElastic ====== */
        @keyframes easeOutElastic {
            0% {
                transform: scale(1);
            }

            30% {
                transform: scale(1.25);
            }

            50% {
                transform: scale(0.95);
            }

            70% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        /* ====== 3A. 自动播放版本（加载后播放一次） ====== */
        .elastic-auto {
            padding: 14px 30px;
            border: none;
            border-radius: 8px;
            background: #00aaff;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
            animation: easeOutElastic 1.2s ease-out forwards;
        }

        /* ====== 3B. 悬停触发版本（每次 hover 时播放） ====== */
        .elastic-hover {
            padding: 14px 30px;
            border: none;
            border-radius: 8px;
            background: #ff7a00;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
            /* 先禁用默认动画，hover 时再添加 */
            transition: none;
        }

        .elastic-hover:hover {
            animation: easeOutElastic 1.2s ease-out forwards;
        }
    </style>
</head>

<body>


    <!-- ====== 悬停播放按钮（将鼠标移上来试试） ====== -->
    <button class="elastic-hover" style="margin-left:40px;">悬停播放</button>

</body>

</html>