// 主要交互脚本
let currentDemo = 'overview';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeTabs();
    initializeTemperatureSlider();
    loadCoreCodeLibrary();

    // 初始化动物选择器
    setTimeout(() => {
        selectAnimal('cat');
    }, 100);
});

// 初始化标签切换
function initializeTabs() {
    const tabs = document.querySelectorAll('.tab-btn');
    const sections = document.querySelectorAll('.demo-section');

    tabs.forEach(tab => {
        tab.addEventListener('click', function () {
            const demoType = this.dataset.demo;

            // 更新标签状态
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 更新内容区域
            sections.forEach(s => s.classList.remove('active'));
            document.getElementById(`${demoType}-section`).classList.add('active');

            currentDemo = demoType;
        });
    });
}

// 初始化温度滑块
function initializeTemperatureSlider() {
    const temperatureSlider = document.getElementById('temperature-slider');
    const temperatureValue = document.getElementById('temperature-value');

    if (!temperatureSlider || !temperatureValue) return;

    // 初始化显示
    updateTemperatureDisplay(parseFloat(temperatureSlider.value));

    // 滑块变化事件
    temperatureSlider.addEventListener('input', function () {
        const temperature = parseFloat(this.value);
        temperatureValue.textContent = temperature.toFixed(1);
        updateTemperatureDisplay(temperature);
    });
}

// 不同场景的logits数据
const scenarioData = {
    cat: [
        { label: '猫', value: 4.0, emoji: '🐱' },
        { label: '狗', value: 3.0, emoji: '🐶' },
        { label: '鸟', value: 2.0, emoji: '🐦' },
        { label: '鱼', value: 1.0, emoji: '🐟' }
    ],
    food: [
        { label: '苹果', value: 3.8, emoji: '🍎' },
        { label: '香蕉', value: 3.2, emoji: '🍌' },
        { label: '橙子', value: 2.5, emoji: '🍊' },
        { label: '葡萄', value: 1.8, emoji: '🍇' }
    ],
    weather: [
        { label: '晴天', value: 4.2, emoji: '☀️' },
        { label: '多云', value: 2.8, emoji: '☁️' },
        { label: '雨天', value: 2.1, emoji: '🌧️' },
        { label: '雪天', value: 1.2, emoji: '❄️' }
    ]
};

let currentScenario = 'cat';

// 选择动物场景
function selectAnimal(scenario) {
    currentScenario = scenario;

    // 更新按钮状态
    const buttons = document.querySelectorAll('.animal-btn');
    buttons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.animal === scenario) {
            btn.classList.add('active');
        }
    });

    // 更新logits显示
    updateLogitsDisplay(scenario);

    // 更新概率显示
    const temperatureSlider = document.getElementById('temperature-slider');
    if (temperatureSlider) {
        updateTemperatureDisplay(parseFloat(temperatureSlider.value));
    }
}

// 更新logits显示
function updateLogitsDisplay(scenario) {
    const logitsDisplay = document.getElementById('logits-display');
    if (!logitsDisplay) return;

    const data = scenarioData[scenario];
    logitsDisplay.innerHTML = '';

    data.forEach(item => {
        const logitBar = document.createElement('div');
        logitBar.className = 'logit-bar';

        const widthPercent = (item.value / 5.0) * 100; // 假设最大值为5.0

        logitBar.innerHTML = `
            <span class="label">${item.emoji} ${item.label}</span>
            <div class="bar">
                <div class="fill" style="width: ${widthPercent}%"></div>
            </div>
            <span class="value">${item.value}</span>
        `;

        logitsDisplay.appendChild(logitBar);
    });
}

// 更新温度显示
function updateTemperatureDisplay(temperature) {
    // 获取当前场景的logits数据
    const logitsData = scenarioData[currentScenario];

    // 计算softmax概率
    const logits = logitsData.map(item => item.value);
    const probabilities = softmaxWithTemperature(logits, temperature);

    const probabilityDisplay = document.getElementById('probability-display');
    if (!probabilityDisplay) return;

    // 清空显示区域
    probabilityDisplay.innerHTML = '';

    // 创建概率条
    logitsData.forEach((item, index) => {
        const prob = probabilities[index];
        const percentage = (prob * 100).toFixed(1);

        const probBar = document.createElement('div');
        probBar.className = 'prob-bar';

        // 根据概率大小设置颜色
        let fillColor = 'linear-gradient(90deg, #667eea, #764ba2)';
        if (prob > 0.5) {
            fillColor = 'linear-gradient(90deg, #27ae60, #2ecc71)';
        } else if (prob > 0.3) {
            fillColor = 'linear-gradient(90deg, #f39c12, #e67e22)';
        } else if (prob > 0.1) {
            fillColor = 'linear-gradient(90deg, #e74c3c, #c0392b)';
        }

        probBar.innerHTML = `
            <span class="label">${item.emoji} ${item.label}</span>
            <div class="bar">
                <div class="fill" style="width: ${prob * 100}%; background: ${fillColor}"></div>
            </div>
            <span class="value">${percentage}%</span>
        `;

        probabilityDisplay.appendChild(probBar);
    });

    // 更新解释卡片
    updateExplanationCards(temperature);
}

// Softmax函数（带温度参数）
function softmaxWithTemperature(logits, temperature) {
    const expValues = logits.map(logit => Math.exp(logit / temperature));
    const sum = expValues.reduce((acc, val) => acc + val, 0);
    return expValues.map(exp => exp / sum);
}

// 更新解释卡片样式
function updateExplanationCards(temperature) {
    const lowTempCard = document.querySelector('.low-temp');
    const highTempCard = document.querySelector('.high-temp');

    if (!lowTempCard || !highTempCard) return;

    // 根据温度值调整卡片样式
    if (temperature <= 1.5) {
        lowTempCard.style.opacity = '1';
        lowTempCard.style.transform = 'scale(1.05)';
        highTempCard.style.opacity = '0.7';
        highTempCard.style.transform = 'scale(1)';
    } else if (temperature >= 3) {
        lowTempCard.style.opacity = '0.7';
        lowTempCard.style.transform = 'scale(1)';
        highTempCard.style.opacity = '1';
        highTempCard.style.transform = 'scale(1.05)';
    } else {
        lowTempCard.style.opacity = '0.8';
        lowTempCard.style.transform = 'scale(1)';
        highTempCard.style.opacity = '0.8';
        highTempCard.style.transform = 'scale(1)';
    }
}

// 加载核心代码库
function loadCoreCodeLibrary() {
    // 确保核心代码库已加载
    if (typeof ModelDistillationCodes === 'undefined') {
        console.error('核心代码库未加载');
        return;
    }

    console.log('核心代码库已加载:', Object.keys(ModelDistillationCodes));
}

// 显示核心代码
function showCoreCode(demoType) {
    const codeData = ModelDistillationCodes[demoType];
    if (!codeData) {
        alert('未找到该演示的核心代码');
        return;
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'code-modal';
    modal.innerHTML = `
        <div class="code-modal-content">
            <div class="code-modal-header">
                <h3>${codeData.title}</h3>
                <button class="code-modal-close" onclick="closeCoreCodeModal()">&times;</button>
            </div>
            <div class="code-modal-body">
                <p class="code-description">${codeData.description}</p>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-language">JavaScript</span>
                        <button class="copy-code-btn" onclick="copyCodeToClipboard('${demoType}')">
                            📋 复制代码
                        </button>
                    </div>
                    <pre class="code-content" id="code-${demoType}"><code>${codeData.code}</code></pre>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 防止背景滚动
    document.body.style.overflow = 'hidden';

    // 点击外部关闭
    modal.addEventListener('click', function (e) {
        if (e.target === modal) {
            closeCoreCodeModal();
        }
    });

    // ESC键关闭
    const escHandler = function (e) {
        if (e.key === 'Escape') {
            closeCoreCodeModal();
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

// 关闭核心代码模态框
function closeCoreCodeModal() {
    const modal = document.querySelector('.code-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// 复制代码到剪贴板
function copyCodeToClipboard(demoType) {
    const codeData = ModelDistillationCodes[demoType];
    if (!codeData) return;

    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = codeData.code;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');

        // 更新按钮文本
        const copyBtn = document.querySelector('.copy-code-btn');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '✅ 已复制!';
        copyBtn.style.background = 'linear-gradient(135deg, #48bb78, #38a169)';

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.style.background = '';
        }, 2000);

    } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择代码');
    }

    document.body.removeChild(textArea);
}

// 蒸馏概览演示 - 知识传递动画
function startDistillationDemo() {
    const teacherModel = document.querySelector('.teacher-model');
    const studentModel = document.querySelector('.student-model');
    const arrow = document.querySelector('.distillation-arrow');
    const particles = document.querySelectorAll('.knowledge-particles .particle');

    if (!teacherModel || !studentModel || !arrow) {
        console.error('找不到模型元素');
        return;
    }

    // 重置状态
    resetDistillationDemo();

    // 创建知识传递动画序列
    const animationSequence = [
        // 第1步：教师模型激活
        () => {
            teacherModel.style.transform = 'scale(1.1)';
            teacherModel.style.boxShadow = '0 20px 60px rgba(231, 76, 60, 0.4)';
            teacherModel.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';

            // 添加脉冲效果
            const layers = teacherModel.querySelectorAll('.layer');
            layers.forEach((layer, index) => {
                setTimeout(() => {
                    layer.style.background = 'linear-gradient(90deg, #ff6b6b, #ee5a52)';
                    layer.style.boxShadow = '0 0 10px rgba(231, 76, 60, 0.5)';
                }, index * 100);
            });
        },

        // 第2步：知识粒子开始流动
        () => {
            arrow.style.transform = 'scale(1.2)';
            particles.forEach((particle, index) => {
                particle.style.background = '#ffd700';
                particle.style.boxShadow = '0 0 15px #ffd700';
                particle.style.animation = 'knowledge-flow 0.8s infinite';
                particle.style.animationDelay = `${index * 0.2}s`;
            });

            // 添加知识传递文字动画
            const arrowText = arrow.querySelector('.arrow-text');
            if (arrowText) {
                arrowText.style.animation = 'pulse-text 1s infinite';
            }
        },

        // 第3步：学生模型接收知识
        () => {
            studentModel.style.transform = 'scale(1.1)';
            studentModel.style.boxShadow = '0 20px 60px rgba(39, 174, 96, 0.4)';
            studentModel.style.background = 'linear-gradient(135deg, #2ecc71, #27ae60)';

            // 学生模型层级逐渐亮起
            const studentLayers = studentModel.querySelectorAll('.layer');
            studentLayers.forEach((layer, index) => {
                setTimeout(() => {
                    layer.style.background = 'linear-gradient(90deg, #2ecc71, #27ae60)';
                    layer.style.boxShadow = '0 0 10px rgba(39, 174, 96, 0.5)';
                    layer.style.transform = 'scaleX(1.1)';
                }, index * 150);
            });
        },

        // 第4步：性能提升动画
        () => {
            // 学生模型统计条动画更新
            const studentStats = studentModel.querySelectorAll('.stat-fill');
            studentStats.forEach((stat, index) => {
                setTimeout(() => {
                    if (stat.classList.contains('student-accuracy')) {
                        stat.style.width = '92%'; // 提升到92%
                        stat.parentElement.nextElementSibling.textContent = '92%';
                    } else if (stat.classList.contains('student-speed')) {
                        stat.style.width = '95%'; // 速度进一步提升
                    } else if (stat.classList.contains('student-resource')) {
                        stat.style.width = '20%'; // 资源消耗进一步降低
                    }
                    stat.style.background = 'linear-gradient(90deg, #f39c12, #e67e22)';
                }, index * 200);
            });
        }
    ];

    // 执行动画序列
    animationSequence.forEach((animation, index) => {
        setTimeout(animation, index * 1500);
    });

    // 5秒后重置
    setTimeout(resetDistillationDemo, 7000);
}

function resetDistillationDemo() {
    const teacherModel = document.querySelector('.teacher-model');
    const studentModel = document.querySelector('.student-model');
    const arrow = document.querySelector('.distillation-arrow');
    const particles = document.querySelectorAll('.knowledge-particles .particle');

    if (teacherModel) {
        teacherModel.style.transform = 'scale(1)';
        teacherModel.style.boxShadow = '';
        teacherModel.style.background = '';

        const teacherLayers = teacherModel.querySelectorAll('.layer');
        teacherLayers.forEach(layer => {
            layer.style.background = 'linear-gradient(90deg, #667eea, #764ba2)';
            layer.style.boxShadow = '';
        });
    }

    if (studentModel) {
        studentModel.style.transform = 'scale(1)';
        studentModel.style.boxShadow = '';
        studentModel.style.background = '';

        const studentLayers = studentModel.querySelectorAll('.layer');
        studentLayers.forEach(layer => {
            layer.style.background = 'linear-gradient(90deg, #667eea, #764ba2)';
            layer.style.boxShadow = '';
            layer.style.transform = 'scaleX(1)';
        });

        // 重置统计数据
        const studentStats = studentModel.querySelectorAll('.stat-fill');
        studentStats.forEach(stat => {
            if (stat.classList.contains('student-accuracy')) {
                stat.style.width = '88%';
                stat.parentElement.nextElementSibling.textContent = '88%';
            } else if (stat.classList.contains('student-speed')) {
                stat.style.width = '85%';
            } else if (stat.classList.contains('student-resource')) {
                stat.style.width = '25%';
            }
            stat.style.background = '';
        });
    }

    if (arrow) {
        arrow.style.transform = 'scale(1)';
        const arrowText = arrow.querySelector('.arrow-text');
        if (arrowText) {
            arrowText.style.animation = '';
        }
    }

    particles.forEach(particle => {
        particle.style.background = 'rgba(255, 255, 255, 0.8)';
        particle.style.boxShadow = '';
        particle.style.animation = 'flow 1.5s infinite';
        particle.style.animationDelay = '';
    });
}

// 温度效果演示 - 交互式温度变化
function animateTemperatureEffect() {
    const slider = document.getElementById('temperature-slider');
    const temperatureValue = document.getElementById('temperature-value');

    if (!slider || !temperatureValue) {
        console.error('找不到温度控制元素');
        return;
    }

    const originalValue = parseFloat(slider.value);
    const temperatures = [0.1, 1.0, 3.0, 5.0, 10.0, originalValue];
    let currentIndex = 0;

    // 禁用滑块
    slider.disabled = true;

    // 创建温度说明提示
    const explanationContainer = document.querySelector('.temperature-explanation');
    const tooltip = document.createElement('div');
    tooltip.className = 'temperature-tooltip';
    tooltip.style.cssText = `
        position: absolute;
        top: -50px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 10px 15px;
        border-radius: 10px;
        font-size: 0.9em;
        font-weight: 600;
        z-index: 1000;
        animation: bounce 0.5s ease;
    `;

    function animateStep() {
        if (currentIndex >= temperatures.length) {
            // 动画结束
            slider.disabled = false;
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
            return;
        }

        const temp = temperatures[currentIndex];
        slider.value = temp;
        temperatureValue.textContent = temp.toFixed(1);
        updateTemperatureDisplay(temp);

        // 更新提示文字
        let tooltipText = '';
        if (temp <= 0.5) {
            tooltipText = '❄️ 极低温：模型非常确定，几乎只选择最可能的答案';
        } else if (temp <= 1.5) {
            tooltipText = '🧊 低温：模型比较确定，主要关注最可能的几个选项';
        } else if (temp <= 3.5) {
            tooltipText = '💧 中温：平衡的选择，既有确定性又有多样性';
        } else if (temp <= 7) {
            tooltipText = '🔥 高温：模型不太确定，会考虑更多可能性';
        } else {
            tooltipText = '💨 极高温：模型非常不确定，几乎随机选择';
        }

        tooltip.textContent = tooltipText;

        // 添加提示到合适位置
        if (!tooltip.parentNode && explanationContainer) {
            explanationContainer.style.position = 'relative';
            explanationContainer.appendChild(tooltip);
        }

        currentIndex++;
        setTimeout(animateStep, 2000); // 每2秒切换一次
    }

    animateStep();
}

// 蒸馏过程演示 - 分步骤交互演示
function startProcessDemo() {
    const steps = document.querySelectorAll('.process-step');
    if (steps.length === 0) {
        console.error('找不到过程步骤元素');
        return;
    }

    let currentStep = 0;

    function executeStep() {
        if (currentStep >= steps.length) {
            // 演示完成，显示成功消息
            showSuccessMessage();
            setTimeout(resetProcessDemo, 2000);
            return;
        }

        const step = steps[currentStep];

        // 高亮当前步骤
        steps.forEach((s, index) => {
            if (index < currentStep) {
                // 已完成的步骤
                s.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
                s.style.color = 'white';
                s.style.transform = 'scale(1)';
            } else if (index === currentStep) {
                // 当前步骤
                s.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
                s.style.color = 'white';
                s.style.transform = 'scale(1.05)';
                s.style.boxShadow = '0 15px 40px rgba(102, 126, 234, 0.3)';
            } else {
                // 未开始的步骤
                s.style.background = 'white';
                s.style.color = '#333';
                s.style.transform = 'scale(1)';
                s.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
            }
        });

        // 执行步骤特定动画
        executeStepAnimation(currentStep);

        currentStep++;
        setTimeout(executeStep, 2500); // 每2.5秒执行下一步
    }

    executeStep();
}

function executeStepAnimation(stepIndex) {
    switch (stepIndex) {
        case 0: // 数据准备
            animateDataPreparation();
            break;
        case 1: // 教师推理
            animateTeacherInference();
            break;
        case 2: // 学生训练
            animateStudentTraining();
            break;
        case 3: // 性能评估
            animatePerformanceEvaluation();
            break;
    }
}

function animateDataPreparation() {
    const dataItems = document.querySelectorAll('.data-item');
    dataItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
            item.style.color = 'white';
            item.style.transform = 'scale(1.1) rotate(5deg)';

            setTimeout(() => {
                item.style.transform = 'scale(1) rotate(0deg)';
            }, 500);
        }, index * 200);
    });
}

function animateTeacherInference() {
    const inputData = document.querySelector('.input-data');
    const softTarget = document.querySelector('.soft-target');
    const hardTarget = document.querySelector('.hard-target');

    // 输入数据闪烁
    if (inputData) {
        inputData.style.animation = 'pulse-glow 1s infinite';
    }

    // 软目标和硬目标依次生成
    setTimeout(() => {
        if (softTarget) {
            softTarget.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
            softTarget.style.color = 'white';
            softTarget.style.transform = 'scale(1.1)';
        }
    }, 800);

    setTimeout(() => {
        if (hardTarget) {
            hardTarget.style.background = 'linear-gradient(135deg, #9b59b6, #8e44ad)';
            hardTarget.style.color = 'white';
            hardTarget.style.transform = 'scale(1.1)';
        }
    }, 1200);
}

function animateStudentTraining() {
    const distillationLoss = document.querySelector('.loss-fill.distillation');
    const taskLoss = document.querySelector('.loss-fill.task');

    // 模拟训练过程中损失的动态变化
    let iteration = 0;
    const maxIterations = 10;

    const trainingInterval = setInterval(() => {
        const progress = iteration / maxIterations;

        // 损失逐渐减少
        const distillationWidth = 60 - (progress * 40); // 从60%减少到20%
        const taskWidth = 40 - (progress * 25); // 从40%减少到15%

        if (distillationLoss) {
            distillationLoss.style.width = distillationWidth + '%';
            distillationLoss.style.background = `linear-gradient(90deg,
                hsl(${240 + progress * 60}, 70%, 60%),
                hsl(${240 + progress * 60}, 70%, 50%))`;
        }

        if (taskLoss) {
            taskLoss.style.width = taskWidth + '%';
            taskLoss.style.background = `linear-gradient(90deg,
                hsl(${120 + progress * 60}, 70%, 60%),
                hsl(${120 + progress * 60}, 70%, 50%))`;
        }

        iteration++;

        if (iteration >= maxIterations) {
            clearInterval(trainingInterval);
        }
    }, 150);
}

function animatePerformanceEvaluation() {
    const metrics = document.querySelectorAll('.metric');

    metrics.forEach((metric, index) => {
        setTimeout(() => {
            metric.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
            metric.style.color = 'white';
            metric.style.transform = 'scale(1.05)';

            // 添加成功图标
            const icon = document.createElement('span');
            icon.textContent = '✅';
            icon.style.marginLeft = '10px';
            metric.appendChild(icon);

        }, index * 300);
    });
}

function showSuccessMessage() {
    const processContainer = document.querySelector('.distillation-process');
    if (!processContainer) return;

    const successMessage = document.createElement('div');
    successMessage.className = 'success-message';
    successMessage.innerHTML = `
        <div style="
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-size: 1.2em;
            font-weight: 600;
            box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
            animation: slideIn 0.5s ease;
        ">
            🎉 模型蒸馏完成！学生模型已成功学习到教师模型的知识！
        </div>
    `;

    processContainer.appendChild(successMessage);

    setTimeout(() => {
        if (successMessage.parentNode) {
            successMessage.parentNode.removeChild(successMessage);
        }
    }, 3000);
}

function resetProcessDemo() {
    const steps = document.querySelectorAll('.process-step');

    // 重置所有步骤样式
    steps.forEach(step => {
        step.style.background = 'white';
        step.style.color = '#333';
        step.style.transform = 'scale(1)';
        step.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
    });

    // 重置数据项
    const dataItems = document.querySelectorAll('.data-item');
    dataItems.forEach(item => {
        item.style.background = '#f8f9fa';
        item.style.color = '#5a6c7d';
        item.style.transform = 'scale(1)';
    });

    // 重置教师推理元素
    const inputData = document.querySelector('.input-data');
    const softTarget = document.querySelector('.soft-target');
    const hardTarget = document.querySelector('.hard-target');

    if (inputData) {
        inputData.style.animation = '';
        inputData.style.background = '#e3f2fd';
        inputData.style.color = '#1976d2';
    }

    if (softTarget) {
        softTarget.style.background = '#fff3e0';
        softTarget.style.color = '#f57c00';
        softTarget.style.transform = 'scale(1)';
    }

    if (hardTarget) {
        hardTarget.style.background = '#f3e5f5';
        hardTarget.style.color = '#7b1fa2';
        hardTarget.style.transform = 'scale(1)';
    }

    // 重置损失条
    const distillationLoss = document.querySelector('.loss-fill.distillation');
    const taskLoss = document.querySelector('.loss-fill.task');

    if (distillationLoss) {
        distillationLoss.style.width = '60%';
        distillationLoss.style.background = 'linear-gradient(90deg, #667eea, #764ba2)';
    }

    if (taskLoss) {
        taskLoss.style.width = '40%';
        taskLoss.style.background = 'linear-gradient(90deg, #27ae60, #2ecc71)';
    }

    // 重置评估指标
    const metrics = document.querySelectorAll('.metric');
    metrics.forEach(metric => {
        metric.style.background = '#f8f9fa';
        metric.style.color = '#333';
        metric.style.transform = 'scale(1)';

        // 移除成功图标
        const icon = metric.querySelector('span');
        if (icon && icon.textContent === '✅') {
            metric.removeChild(icon);
        }
    });

    // 移除成功消息
    const successMessage = document.querySelector('.success-message');
    if (successMessage && successMessage.parentNode) {
        successMessage.parentNode.removeChild(successMessage);
    }
}

// 导出函数供全局使用
window.showCoreCode = showCoreCode;
window.closeCoreCodeModal = closeCoreCodeModal;
window.copyCodeToClipboard = copyCodeToClipboard;
window.startDistillationDemo = startDistillationDemo;
window.resetDistillationDemo = resetDistillationDemo;
window.animateTemperatureEffect = animateTemperatureEffect;
window.startProcessDemo = startProcessDemo;
window.resetProcessDemo = resetProcessDemo;
window.selectAnimal = selectAnimal;
