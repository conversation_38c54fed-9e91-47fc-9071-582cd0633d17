<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贝塞尔曲线交互式学习平台</title>
    <link rel="stylesheet" href="bezier-curves.css">
</head>

<body>
    <!-- 背景动画 -->
    <div class="background-animation">
        <div class="floating-curve"></div>
        <div class="floating-curve"></div>
        <div class="floating-curve"></div>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 标题区域 -->
        <header class="header">
            <h1 class="main-title">
                <span class="title-icon">📐</span>
                贝塞尔曲线交互式学习平台
                <span class="title-subtitle">Bézier Curves Interactive Learning</span>
            </h1>
            <p class="header-description">
                探索数学之美，掌握曲线艺术 - 从线性到四次贝塞尔曲线的完整学习之旅
            </p>
        </header>

        <!-- 导航标签 -->
        <nav class="curve-tabs">
            <button class="tab-btn active" data-curve="linear">
                <span class="tab-icon">📏</span>
                线性贝塞尔曲线
            </button>
            <button class="tab-btn" data-curve="quadratic">
                <span class="tab-icon">📐</span>
                二次贝塞尔曲线
            </button>
            <button class="tab-btn" data-curve="cubic">
                <span class="tab-icon">🎨</span>
                三次贝塞尔曲线
            </button>
            <button class="tab-btn" data-curve="quartic">
                <span class="tab-icon">✨</span>
                四次贝塞尔曲线
            </button>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 线性贝塞尔曲线 -->
            <section class="curve-section active" id="linear-section">
                <div class="content-layout">
                    <div class="canvas-container">
                        <h3 class="canvas-title">📏 线性贝塞尔曲线演示</h3>
                        <canvas id="linear-canvas" width="600" height="400"></canvas>
                        <div class="canvas-controls">
                            <button class="control-btn" onclick="animateLinear()">
                                <span class="btn-icon">▶️</span>
                                播放动画
                            </button>
                            <button class="control-btn" onclick="resetLinear()">
                                <span class="btn-icon">🔄</span>
                                重置
                            </button>
                            <button class="control-btn core-code-btn" onclick="showCoreCode('linear')">
                                <span class="btn-icon">💻</span>
                                核心JS代码
                            </button>
                            <div class="speed-control">
                                <label>动画速度：</label>
                                <input type="range" id="linear-speed" min="0.5" max="3" step="0.1" value="1">
                                <span id="linear-speed-value">1.0x</span>
                            </div>
                        </div>
                    </div>

                    <div class="knowledge-panel">
                        <h3 class="knowledge-title">📚 线性贝塞尔曲线知识详解</h3>

                        <div class="knowledge-section">
                            <h4>🎯 基本概念</h4>
                            <p>线性贝塞尔曲线是最简单的贝塞尔曲线，由两个控制点定义。它实际上就是连接两点的直线段，是所有贝塞尔曲线的基础。</p>
                        </div>

                        <div class="knowledge-section">
                            <h4>📐 数学公式</h4>
                            <div class="formula-box">
                                <p><strong>参数方程：</strong></p>
                                <p class="formula">B(t) = (1-t)P₀ + tP₁</p>
                                <p class="formula-desc">其中 t ∈ [0,1]，P₀和P₁是两个控制点</p>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>🔍 几何意义</h4>
                            <ul class="feature-list">
                                <li><strong>插值性质：</strong>曲线通过起点和终点</li>
                                <li><strong>线性变化：</strong>参数t线性变化时，点在直线上均匀移动</li>
                                <li><strong>凸包性质：</strong>曲线完全位于控制点构成的凸包内</li>
                            </ul>
                        </div>

                        <div class="knowledge-section">
                            <h4>🎨 实际应用</h4>
                            <ul class="application-list">
                                <li>计算机图形学中的线段绘制</li>
                                <li>动画中的线性插值</li>
                                <li>UI设计中的简单过渡效果</li>
                                <li>游戏开发中的直线路径规划</li>
                            </ul>
                        </div>

                        <div class="knowledge-section">
                            <h4>💡 操作提示</h4>
                            <div class="tips-box">
                                <p>🖱️ <strong>拖拽控制点：</strong>点击并拖动红色圆点改变曲线形状</p>
                                <p>▶️ <strong>播放动画：</strong>观察参数t从0到1的变化过程</p>
                                <p>⚡ <strong>调节速度：</strong>使用滑块控制动画播放速度</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 二次贝塞尔曲线 -->
            <section class="curve-section" id="quadratic-section">
                <div class="content-layout">
                    <div class="canvas-container">
                        <h3 class="canvas-title">📐 二次贝塞尔曲线演示</h3>
                        <canvas id="quadratic-canvas" width="600" height="400"></canvas>
                        <div class="canvas-controls">
                            <button class="control-btn" onclick="animateQuadratic()">
                                <span class="btn-icon">▶️</span>
                                播放动画
                            </button>
                            <button class="control-btn" onclick="resetQuadratic()">
                                <span class="btn-icon">🔄</span>
                                重置
                            </button>
                            <button class="control-btn core-code-btn" onclick="showCoreCode('quadratic')">
                                <span class="btn-icon">💻</span>
                                核心JS代码
                            </button>
                            <div class="speed-control">
                                <label>动画速度：</label>
                                <input type="range" id="quadratic-speed" min="0.5" max="3" step="0.1" value="1">
                                <span id="quadratic-speed-value">1.0x</span>
                            </div>
                            <div class="toggle-control">
                                <label>
                                    <input type="checkbox" id="show-construction" checked>
                                    显示构造过程
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="knowledge-panel">
                        <h3 class="knowledge-title">📚 二次贝塞尔曲线知识详解</h3>

                        <div class="knowledge-section">
                            <h4>🎯 基本概念</h4>
                            <p>二次贝塞尔曲线由三个控制点定义，形成一条平滑的抛物线。它是最常用的贝塞尔曲线之一，在字体设计和图形绘制中应用广泛。</p>
                        </div>

                        <div class="knowledge-section">
                            <h4>📐 数学公式</h4>
                            <div class="formula-box">
                                <p><strong>参数方程：</strong></p>
                                <p class="formula">B(t) = (1-t)²P₀ + 2(1-t)tP₁ + t²P₂</p>
                                <p class="formula-desc">其中 t ∈ [0,1]，P₀、P₁、P₂是三个控制点</p>
                                <p><strong>德卡斯特里奥算法：</strong></p>
                                <p class="formula">Q₀ = (1-t)P₀ + tP₁</p>
                                <p class="formula">Q₁ = (1-t)P₁ + tP₂</p>
                                <p class="formula">B(t) = (1-t)Q₀ + tQ₁</p>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>🔍 几何特性</h4>
                            <ul class="feature-list">
                                <li><strong>端点插值：</strong>曲线通过P₀和P₂，但不通过P₁</li>
                                <li><strong>切线性质：</strong>起点切线方向为P₀P₁，终点切线方向为P₁P₂</li>
                                <li><strong>凸包性质：</strong>曲线位于三个控制点构成的三角形内</li>
                                <li><strong>对称性：</strong>关于参数t=0.5具有某种对称性</li>
                            </ul>
                        </div>

                        <div class="knowledge-section">
                            <h4>🎨 实际应用</h4>
                            <ul class="application-list">
                                <li>TrueType字体的字符轮廓定义</li>
                                <li>SVG路径中的quadraticCurveTo</li>
                                <li>CSS动画的缓动函数</li>
                                <li>游戏中的抛物线轨迹</li>
                                <li>UI设计中的平滑过渡</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 三次贝塞尔曲线 -->
            <section class="curve-section" id="cubic-section">
                <div class="content-layout">
                    <div class="canvas-container">
                        <h3 class="canvas-title">🎨 三次贝塞尔曲线演示</h3>
                        <canvas id="cubic-canvas" width="600" height="400"></canvas>
                        <div class="canvas-controls">
                            <button class="control-btn" onclick="animateCubic()">
                                <span class="btn-icon">▶️</span>
                                播放动画
                            </button>
                            <button class="control-btn" onclick="resetCubic()">
                                <span class="btn-icon">🔄</span>
                                重置
                            </button>
                            <button class="control-btn core-code-btn" onclick="showCoreCode('cubic')">
                                <span class="btn-icon">💻</span>
                                核心JS代码
                            </button>
                            <div class="speed-control">
                                <label>动画速度：</label>
                                <input type="range" id="cubic-speed" min="0.5" max="3" step="0.1" value="1">
                                <span id="cubic-speed-value">1.0x</span>
                            </div>
                            <div class="toggle-control">
                                <label>
                                    <input type="checkbox" id="show-cubic-construction" checked>
                                    显示构造过程
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="knowledge-panel">
                        <h3 class="knowledge-title">📚 三次贝塞尔曲线知识详解</h3>

                        <div class="knowledge-section">
                            <h4>🎯 基本概念</h4>
                            <p>三次贝塞尔曲线是最重要的贝塞尔曲线，由四个控制点定义。它在计算机图形学、动画和设计领域应用最为广泛，能够创造出非常平滑和优美的曲线。</p>
                        </div>

                        <div class="knowledge-section">
                            <h4>📐 数学公式</h4>
                            <div class="formula-box">
                                <p><strong>参数方程：</strong></p>
                                <p class="formula">B(t) = (1-t)³P₀ + 3(1-t)²tP₁ + 3(1-t)t²P₂ + t³P₃</p>
                                <p class="formula-desc">其中 t ∈ [0,1]，P₀、P₁、P₂、P₃是四个控制点</p>
                                <p><strong>伯恩斯坦基函数形式：</strong></p>
                                <p class="formula">B(t) = Σ(i=0 to 3) C(3,i) * (1-t)^(3-i) * t^i * P_i</p>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>🔍 几何特性</h4>
                            <ul class="feature-list">
                                <li><strong>端点插值：</strong>曲线通过P₀和P₃</li>
                                <li><strong>切线控制：</strong>P₁控制起点切线，P₂控制终点切线</li>
                                <li><strong>局部控制：</strong>移动一个控制点只影响局部曲线形状</li>
                                <li><strong>变分递减：</strong>曲线的振荡不超过控制多边形</li>
                                <li><strong>仿射不变：</strong>对控制点的仿射变换等于对曲线的仿射变换</li>
                            </ul>
                        </div>

                        <div class="knowledge-section">
                            <h4>🎨 实际应用</h4>
                            <ul class="application-list">
                                <li>PostScript和PDF中的路径定义</li>
                                <li>CSS动画的cubic-bezier缓动函数</li>
                                <li>Adobe Illustrator等矢量图形软件</li>
                                <li>3D建模中的曲面构造</li>
                                <li>字体设计和排版系统</li>
                                <li>游戏和动画中的平滑路径</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 四次贝塞尔曲线 -->
            <section class="curve-section" id="quartic-section">
                <div class="content-layout">
                    <div class="canvas-container">
                        <h3 class="canvas-title">✨ 四次贝塞尔曲线演示</h3>
                        <canvas id="quartic-canvas" width="600" height="400"></canvas>
                        <div class="canvas-controls">
                            <button class="control-btn" onclick="animateQuartic()">
                                <span class="btn-icon">▶️</span>
                                播放动画
                            </button>
                            <button class="control-btn" onclick="resetQuartic()">
                                <span class="btn-icon">🔄</span>
                                重置
                            </button>
                            <button class="control-btn core-code-btn" onclick="showCoreCode('quartic')">
                                <span class="btn-icon">💻</span>
                                核心JS代码
                            </button>
                            <div class="speed-control">
                                <label>动画速度：</label>
                                <input type="range" id="quartic-speed" min="0.5" max="3" step="0.1" value="1">
                                <span id="quartic-speed-value">1.0x</span>
                            </div>
                            <div class="toggle-control">
                                <label>
                                    <input type="checkbox" id="show-quartic-construction" checked>
                                    显示构造过程
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="knowledge-panel">
                        <h3 class="knowledge-title">📚 四次贝塞尔曲线知识详解</h3>

                        <div class="knowledge-section">
                            <h4>🎯 基本概念</h4>
                            <p>四次贝塞尔曲线由五个控制点定义，提供了更高的灵活性和控制精度。虽然在实际应用中不如三次贝塞尔曲线常见，但在需要更复杂曲线形状的场合非常有用。</p>
                        </div>

                        <div class="knowledge-section">
                            <h4>📐 数学公式</h4>
                            <div class="formula-box">
                                <p><strong>参数方程：</strong></p>
                                <p class="formula">B(t) = (1-t)⁴P₀ + 4(1-t)³tP₁ + 6(1-t)²t²P₂ + 4(1-t)t³P₃ + t⁴P₄</p>
                                <p class="formula-desc">其中 t ∈ [0,1]，P₀到P₄是五个控制点</p>
                                <p><strong>伯恩斯坦基函数：</strong></p>
                                <p class="formula">B₄,₀(t) = (1-t)⁴, B₄,₁(t) = 4(1-t)³t, B₄,₂(t) = 6(1-t)²t², B₄,₃(t) =
                                    4(1-t)t³, B₄,₄(t) = t⁴</p>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>🔍 几何特性</h4>
                            <ul class="feature-list">
                                <li><strong>更高自由度：</strong>五个控制点提供更精细的形状控制</li>
                                <li><strong>端点插值：</strong>曲线通过P₀和P₄</li>
                                <li><strong>切线控制：</strong>P₁和P₂影响起点切线，P₃和P₄影响终点切线</li>
                                <li><strong>复杂形状：</strong>可以创造出更复杂的S形和波浪形曲线</li>
                                <li><strong>计算复杂：</strong>相比低次曲线，计算量更大</li>
                            </ul>
                        </div>

                        <div class="knowledge-section">
                            <h4>🎨 实际应用</h4>
                            <ul class="application-list">
                                <li>高精度CAD系统中的曲线设计</li>
                                <li>动画中的复杂运动路径</li>
                                <li>艺术创作和数字雕塑</li>
                                <li>科学可视化中的数据拟合</li>
                                <li>游戏中的特殊效果轨迹</li>
                                <li>建筑设计中的曲面造型</li>
                            </ul>
                        </div>

                        <div class="knowledge-section">
                            <h4>⚠️ 使用注意</h4>
                            <div class="warning-box">
                                <p>🔸 <strong>计算成本：</strong>四次贝塞尔曲线计算复杂度较高</p>
                                <p>🔸 <strong>控制难度：</strong>五个控制点的协调需要更多经验</p>
                                <p>🔸 <strong>替代方案：</strong>通常可用多段三次贝塞尔曲线替代</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <p>🎨 贝塞尔曲线交互式学习平台 | 探索数学之美，掌握曲线艺术</p>
            <p>💡 拖拽控制点体验曲线变化，点击播放按钮观看构造动画</p>
        </footer>
    </div>

    <script src="bezier-core-codes.js"></script>
    <script src="bezier-curves.js"></script>
</body>

</html>